namespace CrmHistorySystem.Core.Models;

/// <summary>
/// Represents the result of a history query with pagination information and performance metrics.
/// </summary>
/// <typeparam name="T">The type of data returned in the result</typeparam>
public class HistoryResult<T>
{
    /// <summary>
    /// The actual data returned by the query
    /// </summary>
    public IEnumerable<T> Data { get; set; } = Enumerable.Empty<T>();

    /// <summary>
    /// Total number of records that match the query (across all pages)
    /// </summary>
    public long TotalCount { get; set; }

    /// <summary>
    /// Indicates if there are more records available beyond the current page
    /// </summary>
    public bool HasMore { get; set; }

    /// <summary>
    /// Current page number (1-based)
    /// </summary>
    public int CurrentPage { get; set; }

    /// <summary>
    /// Number of records per page
    /// </summary>
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages available
    /// </summary>
    public int TotalPages => PageSize > 0 ? (int)Math.Ceiling((double)TotalCount / PageSize) : 0;

    /// <summary>
    /// Storage tiers that were queried to produce this result
    /// </summary>
    public IEnumerable<StorageTier> QueriedTiers { get; set; } = Enumerable.Empty<StorageTier>();

    /// <summary>
    /// Performance metrics for the query execution
    /// </summary>
    public QueryPerformanceMetrics Performance { get; set; } = new();

    /// <summary>
    /// Indicates if the result was served from cache
    /// </summary>
    public bool FromCache { get; set; }

    /// <summary>
    /// Cache key used for this result (if applicable)
    /// </summary>
    public string? CacheKey { get; set; }

    /// <summary>
    /// Timestamp when the result was generated
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Creates a successful result with data
    /// </summary>
    /// <param name="data">The query results</param>
    /// <param name="totalCount">Total number of matching records</param>
    /// <param name="currentPage">Current page number</param>
    /// <param name="pageSize">Records per page</param>
    /// <returns>A successful HistoryResult</returns>
    public static HistoryResult<T> Success(
        IEnumerable<T> data, 
        long totalCount, 
        int currentPage = 1, 
        int pageSize = 100)
    {
        var dataList = data.ToList();
        return new HistoryResult<T>
        {
            Data = dataList,
            TotalCount = totalCount,
            CurrentPage = currentPage,
            PageSize = pageSize,
            HasMore = (currentPage * pageSize) < totalCount
        };
    }

    /// <summary>
    /// Creates an empty result
    /// </summary>
    /// <returns>An empty HistoryResult</returns>
    public static HistoryResult<T> Empty()
    {
        return new HistoryResult<T>
        {
            Data = Enumerable.Empty<T>(),
            TotalCount = 0,
            HasMore = false,
            CurrentPage = 1,
            PageSize = 100
        };
    }

    /// <summary>
    /// Creates a cached result
    /// </summary>
    /// <param name="data">The cached data</param>
    /// <param name="totalCount">Total count</param>
    /// <param name="cacheKey">Cache key used</param>
    /// <param name="currentPage">Current page</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>A cached HistoryResult</returns>
    public static HistoryResult<T> FromCache(
        IEnumerable<T> data, 
        long totalCount, 
        string cacheKey,
        int currentPage = 1, 
        int pageSize = 100)
    {
        var result = Success(data, totalCount, currentPage, pageSize);
        result.FromCache = true;
        result.CacheKey = cacheKey;
        return result;
    }
}

/// <summary>
/// Performance metrics for query execution
/// </summary>
public class QueryPerformanceMetrics
{
    /// <summary>
    /// Total time taken to execute the query
    /// </summary>
    public TimeSpan ExecutionTime { get; set; }

    /// <summary>
    /// Time spent querying the database
    /// </summary>
    public TimeSpan DatabaseTime { get; set; }

    /// <summary>
    /// Time spent on cache operations
    /// </summary>
    public TimeSpan CacheTime { get; set; }

    /// <summary>
    /// Time spent on data serialization/deserialization
    /// </summary>
    public TimeSpan SerializationTime { get; set; }

    /// <summary>
    /// Number of database queries executed
    /// </summary>
    public int DatabaseQueries { get; set; }

    /// <summary>
    /// Number of cache hits
    /// </summary>
    public int CacheHits { get; set; }

    /// <summary>
    /// Number of cache misses
    /// </summary>
    public int CacheMisses { get; set; }

    /// <summary>
    /// Memory usage during query execution (in bytes)
    /// </summary>
    public long MemoryUsage { get; set; }

    /// <summary>
    /// Breakdown of execution time by storage tier
    /// </summary>
    public Dictionary<StorageTier, TimeSpan> TierExecutionTimes { get; set; } = new();

    /// <summary>
    /// Additional performance notes or warnings
    /// </summary>
    public List<string> Notes { get; set; } = new();

    /// <summary>
    /// Indicates if the query performance meets SLA requirements
    /// </summary>
    public bool MeetsSla { get; set; } = true;

    /// <summary>
    /// SLA threshold that was used for evaluation (in milliseconds)
    /// </summary>
    public int SlaThresholdMs { get; set; }
}
