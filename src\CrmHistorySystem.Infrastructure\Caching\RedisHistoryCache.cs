using CrmHistorySystem.Core.Configuration;
using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Core.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using StackExchange.Redis;
using System.Diagnostics;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;

namespace CrmHistorySystem.Infrastructure.Caching;

/// <summary>
/// Redis implementation of IHistoryCache for high-performance distributed caching
/// </summary>
public class RedisHistoryCache : IHistoryCache
{
    private readonly ILogger<RedisHistoryCache> _logger;
    private readonly HistoryOptions _options;
    private readonly IConnectionMultiplexer _redis;
    private readonly IDatabase _database;
    private readonly string _keyPrefix;
    private readonly CacheStatistics _statistics;

    public RedisHistoryCache(
        ILogger<RedisHistoryCache> logger,
        IOptions<HistoryOptions> options,
        IConnectionMultiplexer redis)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _redis = redis ?? throw new ArgumentNullException(nameof(redis));
        _database = _redis.GetDatabase();
        _keyPrefix = _options.Cache.KeyPrefix;
        _statistics = new CacheStatistics
        {
            LastReset = DateTime.UtcNow,
            IsConnected = _redis.IsConnected,
            ServerInfo = _redis.GetEndPoints().FirstOrDefault()?.ToString() ?? "Unknown"
        };
    }

    /// <summary>
    /// Gets cached data by key
    /// </summary>
    public async Task<T?> GetAsync<T>(string cacheKey, CancellationToken cancellationToken = default) where T : class
    {
        if (!_options.Cache.Enabled)
            return null;

        var stopwatch = Stopwatch.StartNew();
        var fullKey = GetFullKey(cacheKey);

        try
        {
            var value = await _database.StringGetAsync(fullKey);
            
            if (value.IsNull)
            {
                IncrementCacheMiss();
                return null;
            }

            var result = DeserializeValue<T>(value);
            IncrementCacheHit();
            
            TrackOperation("Get", stopwatch.Elapsed);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving from cache: {Key}", fullKey);
            TrackOperationError("Get");
            return null;
        }
    }

    /// <summary>
    /// Sets data in cache with expiration
    /// </summary>
    public async Task<bool> SetAsync<T>(
        string cacheKey, 
        T data, 
        TimeSpan expiration, 
        CancellationToken cancellationToken = default) where T : class
    {
        if (!_options.Cache.Enabled || data == null)
            return false;

        var stopwatch = Stopwatch.StartNew();
        var fullKey = GetFullKey(cacheKey);

        try
        {
            var serialized = SerializeValue(data);
            var result = await _database.StringSetAsync(fullKey, serialized, expiration);
            
            TrackOperation("Set", stopwatch.Elapsed);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting cache: {Key}", fullKey);
            TrackOperationError("Set");
            return false;
        }
    }

    /// <summary>
    /// Removes a specific cache entry
    /// </summary>
    public async Task<bool> RemoveAsync(string cacheKey, CancellationToken cancellationToken = default)
    {
        if (!_options.Cache.Enabled)
            return false;

        var stopwatch = Stopwatch.StartNew();
        var fullKey = GetFullKey(cacheKey);

        try
        {
            var result = await _database.KeyDeleteAsync(fullKey);
            
            TrackOperation("Remove", stopwatch.Elapsed);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing from cache: {Key}", fullKey);
            TrackOperationError("Remove");
            return false;
        }
    }

    /// <summary>
    /// Removes cache entries matching a pattern
    /// </summary>
    public async Task<int> RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default)
    {
        if (!_options.Cache.Enabled)
            return 0;

        var stopwatch = Stopwatch.StartNew();
        var fullPattern = GetFullKey(pattern);
        var count = 0;

        try
        {
            // Note: This is a simplified implementation. In production, use server-side Lua script
            // for better performance with large key sets
            var endpoints = _redis.GetEndPoints();
            foreach (var endpoint in endpoints)
            {
                var server = _redis.GetServer(endpoint);
                var keys = server.Keys(pattern: fullPattern);
                
                foreach (var key in keys)
                {
                    if (await _database.KeyDeleteAsync(key))
                        count++;
                }
            }
            
            TrackOperation("RemoveByPattern", stopwatch.Elapsed);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing from cache by pattern: {Pattern}", fullPattern);
            TrackOperationError("RemoveByPattern");
            return 0;
        }
    }

    /// <summary>
    /// Generates a cache key for a history query
    /// </summary>
    public string GenerateCacheKey(HistoryQuery query)
    {
        var keyBuilder = new StringBuilder("query:");

        if (query.LeadId.HasValue)
            keyBuilder.Append($"lead:{query.LeadId}:");

        if (!string.IsNullOrWhiteSpace(query.FieldName))
            keyBuilder.Append($"field:{query.FieldName}:");

        if (query.FieldNames != null && query.FieldNames.Any())
            keyBuilder.Append($"fields:{string.Join(",", query.FieldNames)}:");

        if (!string.IsNullOrWhiteSpace(query.ChangedBy))
            keyBuilder.Append($"user:{query.ChangedBy}:");

        if (query.StartDate.HasValue)
            keyBuilder.Append($"from:{query.StartDate.Value:yyyyMMdd}:");

        if (query.EndDate.HasValue)
            keyBuilder.Append($"to:{query.EndDate.Value:yyyyMMdd}:");

        keyBuilder.Append($"page:{query.Page}:size:{query.PageSize}");

        // Add hash to handle complex queries with many parameters
        if (query.Tiers != null || query.MaxResults.HasValue || query.SortBy != SortField.ChangedAt || query.SortOrder != SortOrder.Descending)
        {
            var queryJson = JsonSerializer.Serialize(query);
            using var md5 = MD5.Create();
            var hash = md5.ComputeHash(Encoding.UTF8.GetBytes(queryJson));
            var hashString = Convert.ToBase64String(hash).Replace("/", "_").Replace("+", "-").TrimEnd('=');
            keyBuilder.Append($":hash:{hashString}");
        }

        return keyBuilder.ToString();
    }

    /// <summary>
    /// Generates a cache key for lead-specific queries
    /// </summary>
    public string GenerateLeadCacheKey(int leadId, DateTime? startDate, DateTime? endDate, int page, int pageSize)
    {
        var keyBuilder = new StringBuilder($"lead:{leadId}");

        if (startDate.HasValue)
            keyBuilder.Append($":from:{startDate.Value:yyyyMMdd}");

        if (endDate.HasValue)
            keyBuilder.Append($":to:{endDate.Value:yyyyMMdd}");

        keyBuilder.Append($":page:{page}:size:{pageSize}");

        return keyBuilder.ToString();
    }

    /// <summary>
    /// Generates a cache key for field-specific queries
    /// </summary>
    public string GenerateFieldCacheKey(string fieldName, DateTime? startDate, DateTime? endDate, int page, int pageSize)
    {
        var keyBuilder = new StringBuilder($"field:{fieldName}");

        if (startDate.HasValue)
            keyBuilder.Append($":from:{startDate.Value:yyyyMMdd}");

        if (endDate.HasValue)
            keyBuilder.Append($":to:{endDate.Value:yyyyMMdd}");

        keyBuilder.Append($":page:{page}:size:{pageSize}");

        return keyBuilder.ToString();
    }

    /// <summary>
    /// Invalidates cache entries related to a specific lead
    /// </summary>
    public async Task<int> InvalidateLeadCacheAsync(int leadId, CancellationToken cancellationToken = default)
    {
        return await RemoveByPatternAsync($"*lead:{leadId}*", cancellationToken);
    }

    /// <summary>
    /// Invalidates cache entries related to a specific field
    /// </summary>
    public async Task<int> InvalidateFieldCacheAsync(string fieldName, CancellationToken cancellationToken = default)
    {
        return await RemoveByPatternAsync($"*field:{fieldName}*", cancellationToken);
    }

    /// <summary>
    /// Gets cache statistics and health information
    /// </summary>
    public async Task<CacheStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            // Update connection status
            _statistics.IsConnected = _redis.IsConnected;
            _statistics.Uptime = DateTime.UtcNow - _statistics.LastReset;

            // Get server stats if connected
            if (_statistics.IsConnected)
            {
                var endpoints = _redis.GetEndPoints();
                if (endpoints.Length > 0)
                {
                    var server = _redis.GetServer(endpoints[0]);
                    var info = await server.InfoAsync();
                    
                    // Parse Redis INFO command results
                    foreach (var entry in info)
                    {
                        if (entry.Key == "used_memory")
                            _statistics.MemoryUsage = long.Parse(entry.Value);
                        
                        if (entry.Key == "keyspace_hits")
                            _statistics.TotalHits = long.Parse(entry.Value);
                        
                        if (entry.Key == "keyspace_misses")
                            _statistics.TotalMisses = long.Parse(entry.Value);
                        
                        if (entry.Key == "expired_keys")
                            _statistics.ExpiredEntries = long.Parse(entry.Value);
                        
                        if (entry.Key == "evicted_keys")
                            _statistics.EvictedEntries = long.Parse(entry.Value);
                    }

                    // Count total entries with our prefix
                    var keys = server.Keys(pattern: $"{_keyPrefix}*");
                    _statistics.TotalEntries = keys.LongCount();
                }
            }

            return _statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting cache statistics");
            return _statistics;
        }
    }

    /// <summary>
    /// Warms up the cache with frequently accessed data
    /// </summary>
    public async Task<int> WarmupCacheAsync(IEnumerable<HistoryQuery> queries, CancellationToken cancellationToken = default)
    {
        // This method would be implemented to pre-populate cache with common queries
        // For now, it's a placeholder
        return 0;
    }

    /// <summary>
    /// Clears all cache entries (use with caution)
    /// </summary>
    public async Task<bool> ClearAllAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var endpoints = _redis.GetEndPoints();
            foreach (var endpoint in endpoints)
            {
                var server = _redis.GetServer(endpoint);
                await server.FlushDatabaseAsync();
            }

            // Reset statistics
            _statistics.LastReset = DateTime.UtcNow;
            _statistics.TotalHits = 0;
            _statistics.TotalMisses = 0;
            _statistics.TotalEntries = 0;
            _statistics.MemoryUsage = 0;
            _statistics.ExpiredEntries = 0;
            _statistics.EvictedEntries = 0;
            _statistics.OperationStats.Clear();
            _statistics.TopKeys.Clear();

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache");
            return false;
        }
    }

    // Private helper methods
    private string GetFullKey(string key) => $"{_keyPrefix}{key}";

    private T? DeserializeValue<T>(string value) where T : class
    {
        if (string.IsNullOrEmpty(value))
            return null;

        try
        {
            return JsonSerializer.Deserialize<T>(value);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deserializing cache value");
            return null;
        }
    }

    private string SerializeValue<T>(T value) where T : class
    {
        return JsonSerializer.Serialize(value);
    }

    private void IncrementCacheHit()
    {
        _statistics.TotalHits++;
    }

    private void IncrementCacheMiss()
    {
        _statistics.TotalMisses++;
    }

    private void TrackOperation(string operation, TimeSpan duration)
    {
        if (!_statistics.OperationStats.TryGetValue(operation, out var stats))
        {
            stats = new CacheOperationStats();
            _statistics.OperationStats[operation] = stats;
        }

        stats.Count++;
        stats.TotalDuration += duration;
        
        if (duration < stats.MinDuration)
            stats.MinDuration = duration;
        
        if (duration > stats.MaxDuration)
            stats.MaxDuration = duration;
    }

    private void TrackOperationError(string operation)
    {
        if (!_statistics.OperationStats.TryGetValue(operation, out var stats))
        {
            stats = new CacheOperationStats();
            _statistics.OperationStats[operation] = stats;
        }

        stats.Errors++;
    }
}
