# Architecture Decision Record: CRM History Storage System

## Title
Tiered Storage Architecture for CRM History Data

## Status
Accepted

## Context
Our CRM system currently stores field change history as JSON objects with version-based keys. This approach has led to several critical issues:

1. **Performance**: Query response times exceed 500ms due to JSON parsing overhead
2. **Cost**: Storage costs are unsustainable for projected 1-2 billion records
3. **Queryability**: Cannot efficiently query by lead, field, or date ranges
4. **Maintenance**: Manual archiving processes are error-prone and time-consuming

We need a new architecture that can:
- Reduce query response times to under 100ms for recent data
- Handle 1-2 billion history records efficiently
- Reduce storage and operational costs by 80-90%
- Provide zero data loss during migration with rollback capability

## Decision
We will implement a tiered storage architecture with three distinct tiers:

1. **Hot Tier** (0-3 months)
   - SQL Server with optimized schema and indexes
   - No compression, optimized for speed
   - <50ms query response time SLA
   - Estimated to contain ~15% of total data

2. **Warm Tier** (3-12 months)
   - SQL Server with page compression
   - Monthly partitioning for efficient archival
   - <200ms query response time SLA
   - Estimated to contain ~25% of total data

3. **Cold Tier** (12+ months)
   - Azure Blob Storage with JSON format
   - Optimized for cost and compliance
   - <1000ms query response time SLA
   - Estimated to contain ~60% of total data

Additionally, we will implement:
- Redis caching layer with 1-hour TTL for frequently accessed data
- Automatic tier migration based on data age
- Unified query interface that transparently spans all tiers

## Consequences

### Positive
1. **Performance Improvement**: 
   - Hot tier queries will be 10x faster than current system
   - Caching will further reduce response times for frequent queries
   - Batch operations will be more efficient with relational storage

2. **Cost Reduction**:
   - Cold tier storage is ~95% cheaper than SQL Server
   - Warm tier compression reduces storage by ~70%
   - Overall estimated cost reduction of 85%

3. **Improved Queryability**:
   - Optimized indexes for common query patterns
   - Efficient filtering by lead, field, date ranges
   - Ability to perform analytics across historical data

4. **Operational Benefits**:
   - Automated data lifecycle management
   - Reduced manual intervention for archiving
   - Better monitoring and observability

### Negative
1. **Increased Complexity**:
   - Managing multiple storage tiers adds complexity
   - Need for synchronization between tiers
   - More complex deployment and monitoring

2. **Migration Challenges**:
   - One-time migration of existing JSON data
   - Need for validation and rollback procedures
   - Potential for temporary performance impact during migration

3. **Query Consistency**:
   - Cross-tier queries may have variable performance
   - Need to handle tier boundaries in query logic
   - Potential for inconsistent results during tier transitions

4. **Operational Overhead**:
   - Need to monitor and maintain multiple storage systems
   - Additional infrastructure components (Redis, Azure Storage)
   - More complex backup and disaster recovery procedures

## Alternatives Considered

### 1. NoSQL Document Database (MongoDB/CosmosDB)
- **Pros**: Better JSON handling, horizontal scaling
- **Cons**: Higher cost at scale, less efficient for relational queries
- **Rejection Reason**: Cost projections exceeded SQL Server + Blob hybrid approach

### 2. Single SQL Server with Table Partitioning
- **Pros**: Simpler architecture, consistent query performance
- **Cons**: Higher storage costs, less efficient for old data
- **Rejection Reason**: Would not meet the 80-90% cost reduction target

### 3. Event Sourcing with Event Store
- **Pros**: Natural fit for historical change tracking
- **Cons**: Complex implementation, high learning curve
- **Rejection Reason**: Excessive reengineering required for existing systems

### 4. Data Warehouse Approach (Snowflake/Synapse)
- **Pros**: Excellent for analytics, built-in tiering
- **Cons**: Higher latency, not designed for transactional workloads
- **Rejection Reason**: Could not meet the sub-100ms query requirement

## Implementation Plan

### Phase 1: Core Foundation (Week 1-2)
- Create data models and service interfaces
- Implement validation and error handling
- Set up dependency injection framework

### Phase 2: Storage Implementation (Week 3-4)
- Implement tiered storage services
- Add Redis caching layer
- Create batch processing capabilities

### Phase 3: Database Design (Week 5)
- Create optimized database schema
- Implement partitioning and compression
- Develop migration scripts with validation

### Phase 4: Integration & Testing (Week 6)
- Set up monitoring and observability
- Implement comprehensive test suite
- Create performance benchmarks

### Phase 5: Deployment & Migration (Week 7-8)
- Deploy to staging environment
- Migrate production data in batches
- Validate and cutover to new system

## Metrics & Success Criteria

1. **Performance**:
   - Hot tier: 95th percentile response time < 50ms
   - Warm tier: 95th percentile response time < 200ms
   - Cold tier: 95th percentile response time < 1000ms

2. **Scalability**:
   - Support 10,000+ history entries per second write throughput
   - Handle 1,000+ concurrent query operations
   - Scale to 2+ billion total records

3. **Cost Efficiency**:
   - 80%+ reduction in storage costs
   - 90%+ reduction in database DTU/CPU utilization
   - 70%+ reduction in operational maintenance time

4. **Data Integrity**:
   - Zero data loss during migration
   - 100% validation of migrated records
   - Successful rollback capability if issues occur

## Conclusion
The tiered storage architecture provides the best balance of performance, cost, and scalability for our CRM history storage needs. By intelligently routing queries and migrating data between tiers based on age, we can achieve both the performance targets for recent data and the cost reduction goals for historical data.

## Appendix: Storage Tier Comparison

| Metric | Hot Tier | Warm Tier | Cold Tier |
|--------|----------|-----------|-----------|
| Data Age | 0-3 months | 3-12 months | 12+ months |
| Storage Type | SQL Server | SQL Server (compressed) | Azure Blob Storage |
| Query SLA | <50ms | <200ms | <1000ms |
| Est. Data Volume | 15% | 25% | 60% |
| Cost per GB/month | $0.12 | $0.04 | $0.01 |
| Indexing | Extensive | Moderate | Minimal |
| Compression | None | Page-level | Archive |
| Backup Frequency | Daily | Weekly | Monthly |
| Retention Policy | 90 days | 365 days | 7+ years |
