# CRM History System

A high-performance, scalable CRM history storage system designed to handle 1-2 billion records with sub-100ms query times and 80-90% cost reduction through intelligent tiered storage.

## 🚀 Key Features

- **Tiered Storage Architecture**: Hot (0-3 months), Warm (3-12 months), Cold (12+ months)
- **High Performance**: Sub-50ms queries for recent data, <200ms for historical data
- **Massive Scale**: Handles 1-2 billion history records efficiently
- **Cost Optimized**: 80-90% reduction in storage and operational costs
- **Zero Data Loss**: Comprehensive migration with rollback capabilities
- **Real-time Caching**: Redis-based distributed caching with intelligent invalidation
- **Automatic Archival**: Policy-based data lifecycle management

## 📊 Performance Targets

| Tier | Data Age | Query Response Time | Storage Type |
|------|----------|-------------------|--------------|
| Hot | 0-3 months | < 50ms (95th percentile) | SQL Server (optimized) |
| Warm | 3-12 months | < 200ms (95th percentile) | SQL Server (compressed) |
| Cold | 12+ months | < 1000ms (95th percentile) | Azure Blob Storage |

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Hot Tier      │    │   Warm Tier     │    │   Cold Tier     │
│   SQL Server    │    │   SQL Server    │    │  Azure Blob     │
│   (0-3 months)  │    │   (3-12 months) │    │  (12+ months)   │
│   <50ms SLA     │    │   <200ms SLA    │    │  <1000ms SLA    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Redis Cache    │
                    │  (1 hour TTL)   │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │  API Gateway    │
                    │  Load Balancer  │
                    └─────────────────┘
```

## 🛠️ Technology Stack

- **.NET 6**: High-performance web API framework
- **Entity Framework Core**: ORM with optimized queries
- **SQL Server**: Primary storage for hot and warm tiers
- **Azure Blob Storage**: Cost-effective cold tier storage
- **Redis**: Distributed caching layer
- **Docker**: Containerized deployment
- **Prometheus & Grafana**: Monitoring and alerting

## 🚀 Quick Start

### Prerequisites

- .NET 6 SDK
- Docker & Docker Compose
- SQL Server (or LocalDB for development)
- Redis (optional, will use in-memory cache as fallback)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/crm-history-system.git
   cd crm-history-system
   ```

2. **Start dependencies with Docker**
   ```bash
   docker-compose up -d sqlserver-hot sqlserver-warm redis
   ```

3. **Run database migrations**
   ```bash
   # Hot tier
   sqlcmd -S localhost,1433 -U sa -P "YourStrong@Passw0rd" -i database/migrations/001_CreateHotTierTables.sql
   
   # Warm tier
   sqlcmd -S localhost,1434 -U sa -P "YourStrong@Passw0rd" -i database/migrations/002_CreateWarmTierTables.sql
   ```

4. **Start the API**
   ```bash
   cd src/CrmHistorySystem.Api
   dotnet run
   ```

5. **Access Swagger UI**
   Open https://localhost:7001 in your browser

### Production Deployment

1. **Build and deploy with Docker Compose**
   ```bash
   docker-compose up -d
   ```

2. **Access the application**
   - API: http://localhost:8080
   - Grafana: http://localhost:3000 (admin/admin)
   - Prometheus: http://localhost:9090

## 📝 API Usage

### Query History Entries

```http
POST /api/v1/history/query
Content-Type: application/json

{
  "leadId": 12345,
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-12-31T23:59:59Z",
  "page": 1,
  "pageSize": 100
}
```

### Add Single Entry

```http
POST /api/v1/history/entry
Content-Type: application/json

{
  "leadId": 12345,
  "fieldName": "Email",
  "oldValue": "<EMAIL>",
  "newValue": "<EMAIL>",
  "changedAt": "2024-07-10T10:30:00Z",
  "changedBy": "<EMAIL>"
}
```

### Batch Insert

```http
POST /api/v1/history/batch
Content-Type: application/json

[
  {
    "leadId": 12345,
    "fieldName": "FirstName",
    "oldValue": "John",
    "newValue": "Johnny",
    "changedAt": "2024-07-10T10:30:00Z",
    "changedBy": "<EMAIL>"
  }
]
```

## 🔧 Configuration

### Key Configuration Options

```json
{
  "History": {
    "HotTierRetentionDays": 90,
    "WarmTierRetentionDays": 365,
    "BatchSize": 1000,
    "CacheExpirationMinutes": 60,
    "ConnectionStrings": {
      "Hot": "Server=localhost;Database=CrmHistory_Hot;...",
      "Warm": "Server=localhost;Database=CrmHistory_Warm;...",
      "Cold": "DefaultEndpointsProtocol=https;AccountName=...",
      "Redis": "localhost:6379"
    },
    "Performance": {
      "HotTierMaxResponseTimeMs": 50,
      "WarmTierMaxResponseTimeMs": 200,
      "ColdTierMaxResponseTimeMs": 1000
    }
  }
}
```

## 📊 Monitoring & Observability

### Health Checks

- `/health` - Overall system health
- `/health/ready` - Readiness probe
- `/health/live` - Liveness probe

### Metrics

- `/metrics` - System statistics and performance metrics

### Logging

Structured logging with Serilog:
- Console output for development
- Application Insights for production
- Performance metrics for slow queries

## 🧪 Testing

### Run Unit Tests

```bash
dotnet test tests/CrmHistorySystem.Tests/
```

### Performance Benchmarks

```bash
# Run performance tests
dotnet test tests/CrmHistorySystem.Tests/ --filter "Category=Performance"

# Load testing with NBomber
dotnet run --project tests/CrmHistorySystem.Tests/Performance/
```

### Expected Performance Results

- **Hot Tier Queries**: 95th percentile < 50ms
- **Batch Inserts**: 10,000+ records/second
- **Concurrent Users**: 1,000+ simultaneous connections
- **Memory Usage**: < 2GB under normal load

## 🔒 Security

### Features

- **Input Validation**: FluentValidation for all API inputs
- **SQL Injection Prevention**: Parameterized queries only
- **Field Encryption**: Azure Key Vault integration for sensitive data
- **Audit Logging**: Complete audit trail for all operations
- **Authentication**: JWT Bearer token support

### Encrypted Fields

Configure sensitive fields for automatic encryption:

```json
{
  "History": {
    "Security": {
      "EncryptedFields": [
        "SSN",
        "CreditCardNumber",
        "BankAccount",
        "PersonalNotes"
      ]
    }
  }
}
```

## 📈 Migration from Legacy System

### Migration Process

1. **Assessment**: Analyze current JSON-based storage
2. **Schema Creation**: Deploy new relational schema
3. **Data Migration**: Batch migration with validation
4. **Verification**: Integrity checks and rollback capability
5. **Cutover**: Switch to new system with zero downtime

### Migration Script

```bash
# Run migration from JSON to relational
sqlcmd -S localhost -d CrmHistory_Hot -Q "
EXEC sp_MigrateJsonToRelational 
  @SourceTable = 'LegacyHistoryTable',
  @JsonColumn = 'HistoryData',
  @BatchSize = 10000,
  @ValidateData = 1,
  @CreateBackup = 1
"
```

## 🔄 Data Lifecycle Management

### Automatic Archival

- **Hot → Warm**: After 90 days (configurable)
- **Warm → Cold**: After 365 days (configurable)
- **Permanent Deletion**: After 7 years (compliance requirement)

### Manual Operations

```bash
# Trigger manual archival
curl -X POST "https://api.example.com/api/v1/history/archive?cutoffDate=2023-01-01"

# Validate data integrity
curl -X POST "https://api.example.com/api/v1/history/validate-integrity?sampleSize=1000"
```

## 🚨 Troubleshooting

### Common Issues

1. **Slow Queries**
   - Check tier distribution in query results
   - Verify index usage with query execution plans
   - Monitor cache hit ratios

2. **High Memory Usage**
   - Reduce batch sizes for large operations
   - Check for memory leaks in long-running queries
   - Monitor garbage collection metrics

3. **Cache Issues**
   - Verify Redis connectivity
   - Check cache key patterns and TTL settings
   - Monitor cache hit/miss ratios

### Performance Tuning

1. **Database Optimization**
   ```sql
   -- Update statistics
   UPDATE STATISTICS HistoryEntries_Hot WITH FULLSCAN;
   
   -- Rebuild indexes
   ALTER INDEX ALL ON HistoryEntries_Hot REBUILD;
   ```

2. **Cache Optimization**
   - Adjust TTL based on data access patterns
   - Implement cache warming for frequently accessed data
   - Monitor memory usage and eviction rates

## 📚 Additional Resources

- [API Documentation](docs/api.md)
- [Database Schema](docs/database-schema.md)
- [Performance Tuning Guide](docs/performance-tuning.md)
- [Deployment Guide](docs/deployment.md)
- [Monitoring Runbook](docs/monitoring.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and questions:
- Create an issue in this repository
- Contact the development <NAME_EMAIL>
- Check the [troubleshooting guide](docs/troubleshooting.md)
