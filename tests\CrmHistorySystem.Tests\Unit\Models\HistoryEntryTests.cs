using CrmHistorySystem.Core.Models;
using FluentAssertions;
using Xunit;

namespace CrmHistorySystem.Tests.Unit.Models;

/// <summary>
/// Unit tests for HistoryEntry model
/// </summary>
public class HistoryEntryTests
{
    [Fact]
    public void DetermineStorageTier_RecentEntry_ShouldReturnHotTier()
    {
        // Arrange
        var entry = new HistoryEntry
        {
            ChangedAt = DateTime.UtcNow.AddDays(-30) // 30 days ago
        };

        // Act
        var tier = entry.DetermineStorageTier(hotTierDays: 90, warmTierDays: 365);

        // Assert
        tier.Should().Be(StorageTier.Hot);
    }

    [Fact]
    public void DetermineStorageTier_MediumAgeEntry_ShouldReturnWarmTier()
    {
        // Arrange
        var entry = new HistoryEntry
        {
            ChangedAt = DateTime.UtcNow.AddDays(-180) // 180 days ago
        };

        // Act
        var tier = entry.DetermineStorageTier(hotTierDays: 90, warmTierDays: 365);

        // Assert
        tier.Should().Be(StorageTier.Warm);
    }

    [Fact]
    public void DetermineStorageTier_OldEntry_ShouldReturnColdTier()
    {
        // Arrange
        var entry = new HistoryEntry
        {
            ChangedAt = DateTime.UtcNow.AddDays(-400) // 400 days ago
        };

        // Act
        var tier = entry.DetermineStorageTier(hotTierDays: 90, warmTierDays: 365);

        // Assert
        tier.Should().Be(StorageTier.Cold);
    }

    [Fact]
    public void GenerateChecksum_ValidEntry_ShouldReturnConsistentChecksum()
    {
        // Arrange
        var entry = new HistoryEntry
        {
            LeadId = 12345,
            FieldName = "FirstName",
            OldValue = "John",
            NewValue = "Johnny",
            ChangedAt = new DateTime(2024, 1, 15, 10, 30, 0, DateTimeKind.Utc),
            ChangedBy = "<EMAIL>"
        };

        // Act
        var checksum1 = entry.GenerateChecksum();
        var checksum2 = entry.GenerateChecksum();

        // Assert
        checksum1.Should().NotBeNullOrEmpty();
        checksum1.Should().Be(checksum2); // Should be consistent
    }

    [Fact]
    public void GenerateChecksum_DifferentEntries_ShouldReturnDifferentChecksums()
    {
        // Arrange
        var entry1 = new HistoryEntry
        {
            LeadId = 12345,
            FieldName = "FirstName",
            OldValue = "John",
            NewValue = "Johnny",
            ChangedAt = DateTime.UtcNow,
            ChangedBy = "<EMAIL>"
        };

        var entry2 = new HistoryEntry
        {
            LeadId = 12345,
            FieldName = "FirstName",
            OldValue = "John",
            NewValue = "Jack", // Different new value
            ChangedAt = DateTime.UtcNow,
            ChangedBy = "<EMAIL>"
        };

        // Act
        var checksum1 = entry1.GenerateChecksum();
        var checksum2 = entry2.GenerateChecksum();

        // Assert
        checksum1.Should().NotBe(checksum2);
    }

    [Theory]
    [InlineData(30, 365, StorageTier.Hot)]
    [InlineData(120, 365, StorageTier.Warm)]
    [InlineData(400, 365, StorageTier.Cold)]
    public void DetermineStorageTier_VariousAges_ShouldReturnCorrectTier(
        int daysAgo, int warmTierDays, StorageTier expectedTier)
    {
        // Arrange
        var entry = new HistoryEntry
        {
            ChangedAt = DateTime.UtcNow.AddDays(-daysAgo)
        };

        // Act
        var tier = entry.DetermineStorageTier(hotTierDays: 90, warmTierDays: warmTierDays);

        // Assert
        tier.Should().Be(expectedTier);
    }

    [Fact]
    public void HistoryEntry_DefaultValues_ShouldBeSetCorrectly()
    {
        // Arrange & Act
        var entry = new HistoryEntry();

        // Assert
        entry.Tier.Should().Be(StorageTier.Hot);
        entry.FieldName.Should().Be(string.Empty);
        entry.ChangedBy.Should().Be(string.Empty);
    }

    [Fact]
    public void HistoryEntry_SetProperties_ShouldRetainValues()
    {
        // Arrange
        var leadId = 12345;
        var fieldName = "Email";
        var oldValue = "<EMAIL>";
        var newValue = "<EMAIL>";
        var changedAt = DateTime.UtcNow;
        var changedBy = "<EMAIL>";
        var metadata = "{\"source\":\"api\"}";

        // Act
        var entry = new HistoryEntry
        {
            LeadId = leadId,
            FieldName = fieldName,
            OldValue = oldValue,
            NewValue = newValue,
            ChangedAt = changedAt,
            ChangedBy = changedBy,
            Metadata = metadata
        };

        // Assert
        entry.LeadId.Should().Be(leadId);
        entry.FieldName.Should().Be(fieldName);
        entry.OldValue.Should().Be(oldValue);
        entry.NewValue.Should().Be(newValue);
        entry.ChangedAt.Should().Be(changedAt);
        entry.ChangedBy.Should().Be(changedBy);
        entry.Metadata.Should().Be(metadata);
    }
}
