using System.ComponentModel.DataAnnotations;

namespace CrmHistorySystem.Core.Models;

/// <summary>
/// Represents a batch of history entries for bulk operations with validation and processing metadata.
/// Optimized for high-throughput write operations with data integrity guarantees.
/// </summary>
public class HistoryBatch
{
    /// <summary>
    /// Unique identifier for the batch operation
    /// </summary>
    public Guid BatchId { get; set; } = Guid.NewGuid();

    /// <summary>
    /// Collection of history entries in this batch
    /// </summary>
    [Required]
    public IList<HistoryEntry> Entries { get; set; } = new List<HistoryEntry>();

    /// <summary>
    /// Timestamp when the batch was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// User or system that created this batch
    /// </summary>
    [Required]
    [StringLength(100)]
    public string CreatedBy { get; set; } = string.Empty;

    /// <summary>
    /// Current status of the batch processing
    /// </summary>
    public BatchStatus Status { get; set; } = BatchStatus.Pending;

    /// <summary>
    /// Number of entries successfully processed
    /// </summary>
    public int ProcessedCount { get; set; }

    /// <summary>
    /// Number of entries that failed processing
    /// </summary>
    public int FailedCount { get; set; }

    /// <summary>
    /// Validation errors encountered during processing
    /// </summary>
    public IList<BatchValidationError> ValidationErrors { get; set; } = new List<BatchValidationError>();

    /// <summary>
    /// Processing errors encountered during execution
    /// </summary>
    public IList<BatchProcessingError> ProcessingErrors { get; set; } = new List<BatchProcessingError>();

    /// <summary>
    /// Timestamp when processing started
    /// </summary>
    public DateTime? ProcessingStartedAt { get; set; }

    /// <summary>
    /// Timestamp when processing completed
    /// </summary>
    public DateTime? ProcessingCompletedAt { get; set; }

    /// <summary>
    /// Total processing time
    /// </summary>
    public TimeSpan? ProcessingDuration => 
        ProcessingStartedAt.HasValue && ProcessingCompletedAt.HasValue 
            ? ProcessingCompletedAt - ProcessingStartedAt 
            : null;

    /// <summary>
    /// Metadata for the batch operation
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Retry count for failed batches
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// Maximum number of retries allowed
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Total number of entries in the batch
    /// </summary>
    public int TotalCount => Entries.Count;

    /// <summary>
    /// Success rate as a percentage
    /// </summary>
    public double SuccessRate => TotalCount > 0 ? (double)ProcessedCount / TotalCount * 100 : 0;

    /// <summary>
    /// Validates all entries in the batch
    /// </summary>
    /// <returns>True if all entries are valid</returns>
    public bool ValidateEntries()
    {
        ValidationErrors.Clear();
        var isValid = true;

        for (int i = 0; i < Entries.Count; i++)
        {
            var entry = Entries[i];
            var entryErrors = ValidateEntry(entry, i);
            
            if (entryErrors.Any())
            {
                foreach (var error in entryErrors)
                {
                    ValidationErrors.Add(error);
                }
                isValid = false;
            }
        }

        return isValid;
    }

    /// <summary>
    /// Validates a single entry
    /// </summary>
    /// <param name="entry">Entry to validate</param>
    /// <param name="index">Index of the entry in the batch</param>
    /// <returns>List of validation errors</returns>
    private IEnumerable<BatchValidationError> ValidateEntry(HistoryEntry entry, int index)
    {
        var errors = new List<BatchValidationError>();

        if (entry.LeadId <= 0)
        {
            errors.Add(new BatchValidationError
            {
                EntryIndex = index,
                FieldName = nameof(HistoryEntry.LeadId),
                ErrorMessage = "LeadId must be greater than 0",
                ErrorCode = "INVALID_LEAD_ID"
            });
        }

        if (string.IsNullOrWhiteSpace(entry.FieldName))
        {
            errors.Add(new BatchValidationError
            {
                EntryIndex = index,
                FieldName = nameof(HistoryEntry.FieldName),
                ErrorMessage = "FieldName is required",
                ErrorCode = "MISSING_FIELD_NAME"
            });
        }
        else if (entry.FieldName.Length > 100)
        {
            errors.Add(new BatchValidationError
            {
                EntryIndex = index,
                FieldName = nameof(HistoryEntry.FieldName),
                ErrorMessage = "FieldName cannot exceed 100 characters",
                ErrorCode = "FIELD_NAME_TOO_LONG"
            });
        }

        if (string.IsNullOrWhiteSpace(entry.ChangedBy))
        {
            errors.Add(new BatchValidationError
            {
                EntryIndex = index,
                FieldName = nameof(HistoryEntry.ChangedBy),
                ErrorMessage = "ChangedBy is required",
                ErrorCode = "MISSING_CHANGED_BY"
            });
        }
        else if (entry.ChangedBy.Length > 100)
        {
            errors.Add(new BatchValidationError
            {
                EntryIndex = index,
                FieldName = nameof(HistoryEntry.ChangedBy),
                ErrorMessage = "ChangedBy cannot exceed 100 characters",
                ErrorCode = "CHANGED_BY_TOO_LONG"
            });
        }

        if (entry.ChangedAt == default)
        {
            errors.Add(new BatchValidationError
            {
                EntryIndex = index,
                FieldName = nameof(HistoryEntry.ChangedAt),
                ErrorMessage = "ChangedAt is required",
                ErrorCode = "MISSING_CHANGED_AT"
            });
        }
        else if (entry.ChangedAt > DateTime.UtcNow.AddMinutes(5))
        {
            errors.Add(new BatchValidationError
            {
                EntryIndex = index,
                FieldName = nameof(HistoryEntry.ChangedAt),
                ErrorMessage = "ChangedAt cannot be in the future",
                ErrorCode = "FUTURE_CHANGE_DATE"
            });
        }

        return errors;
    }

    /// <summary>
    /// Generates checksums for all entries in the batch
    /// </summary>
    public void GenerateChecksums()
    {
        foreach (var entry in Entries)
        {
            entry.Checksum = entry.GenerateChecksum();
        }
    }

    /// <summary>
    /// Determines storage tiers for all entries in the batch
    /// </summary>
    /// <param name="hotTierDays">Hot tier retention days</param>
    /// <param name="warmTierDays">Warm tier retention days</param>
    public void DetermineStorageTiers(int hotTierDays = 90, int warmTierDays = 365)
    {
        foreach (var entry in Entries)
        {
            entry.Tier = entry.DetermineStorageTier(hotTierDays, warmTierDays);
        }
    }

    /// <summary>
    /// Groups entries by their storage tier
    /// </summary>
    /// <returns>Dictionary of entries grouped by storage tier</returns>
    public Dictionary<StorageTier, IList<HistoryEntry>> GroupByTier()
    {
        return Entries.GroupBy(e => e.Tier)
                     .ToDictionary(g => g.Key, g => (IList<HistoryEntry>)g.ToList());
    }

    /// <summary>
    /// Marks the batch as processing started
    /// </summary>
    public void StartProcessing()
    {
        Status = BatchStatus.Processing;
        ProcessingStartedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Marks the batch as completed
    /// </summary>
    /// <param name="processedCount">Number of successfully processed entries</param>
    /// <param name="failedCount">Number of failed entries</param>
    public void CompleteProcessing(int processedCount, int failedCount)
    {
        ProcessedCount = processedCount;
        FailedCount = failedCount;
        ProcessingCompletedAt = DateTime.UtcNow;
        
        Status = failedCount == 0 ? BatchStatus.Completed : 
                 processedCount == 0 ? BatchStatus.Failed : 
                 BatchStatus.PartiallyCompleted;
    }

    /// <summary>
    /// Marks the batch as failed
    /// </summary>
    /// <param name="errorMessage">Error message describing the failure</param>
    public void MarkAsFailed(string errorMessage)
    {
        Status = BatchStatus.Failed;
        ProcessingCompletedAt = DateTime.UtcNow;
        
        ProcessingErrors.Add(new BatchProcessingError
        {
            ErrorMessage = errorMessage,
            ErrorCode = "BATCH_PROCESSING_FAILED",
            OccurredAt = DateTime.UtcNow
        });
    }

    /// <summary>
    /// Determines if the batch can be retried
    /// </summary>
    /// <returns>True if the batch can be retried</returns>
    public bool CanRetry()
    {
        return Status == BatchStatus.Failed && RetryCount < MaxRetries;
    }
}

/// <summary>
/// Status of batch processing
/// </summary>
public enum BatchStatus
{
    Pending,
    Processing,
    Completed,
    PartiallyCompleted,
    Failed,
    Cancelled
}

/// <summary>
/// Represents a validation error for a specific entry in a batch
/// </summary>
public class BatchValidationError
{
    public int EntryIndex { get; set; }
    public string FieldName { get; set; } = string.Empty;
    public string ErrorMessage { get; set; } = string.Empty;
    public string ErrorCode { get; set; } = string.Empty;
    public DateTime OccurredAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Represents a processing error during batch execution
/// </summary>
public class BatchProcessingError
{
    public string ErrorMessage { get; set; } = string.Empty;
    public string ErrorCode { get; set; } = string.Empty;
    public string? StackTrace { get; set; }
    public DateTime OccurredAt { get; set; } = DateTime.UtcNow;
    public int? EntryIndex { get; set; }
}
