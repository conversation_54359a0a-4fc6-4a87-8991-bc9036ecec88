using System.ComponentModel.DataAnnotations;

namespace CrmHistorySystem.Core.Configuration;

/// <summary>
/// Configuration options for the CRM History System.
/// Defines retention policies, performance settings, and storage tier configurations.
/// </summary>
public class HistoryOptions
{
    /// <summary>
    /// Configuration section name in appsettings.json
    /// </summary>
    public const string SectionName = "History";

    /// <summary>
    /// Number of days to retain data in the hot tier (high-performance storage)
    /// </summary>
    [Range(1, 365)]
    public int HotTierRetentionDays { get; set; } = 90;

    /// <summary>
    /// Number of days to retain data in the warm tier (medium-performance storage)
    /// </summary>
    [Range(1, 3650)]
    public int WarmTierRetentionDays { get; set; } = 365;

    /// <summary>
    /// Batch size for bulk operations
    /// </summary>
    [Range(100, 10000)]
    public int BatchSize { get; set; } = 1000;

    /// <summary>
    /// Cache expiration time in minutes
    /// </summary>
    [Range(1, 1440)]
    public int CacheExpirationMinutes { get; set; } = 60;

    /// <summary>
    /// Maximum number of concurrent database connections
    /// </summary>
    [Range(1, 1000)]
    public int MaxConcurrentConnections { get; set; } = 100;

    /// <summary>
    /// Command timeout in seconds for database operations
    /// </summary>
    [Range(30, 3600)]
    public int CommandTimeoutSeconds { get; set; } = 300;

    /// <summary>
    /// Maximum retry attempts for failed operations
    /// </summary>
    [Range(0, 10)]
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Initial retry delay in milliseconds
    /// </summary>
    [Range(100, 10000)]
    public int InitialRetryDelayMs { get; set; } = 100;

    /// <summary>
    /// Maximum retry delay in milliseconds
    /// </summary>
    [Range(1000, 60000)]
    public int MaxRetryDelayMs { get; set; } = 5000;

    /// <summary>
    /// Connection strings for different storage tiers
    /// </summary>
    public ConnectionStrings ConnectionStrings { get; set; } = new();

    /// <summary>
    /// Performance SLA thresholds in milliseconds
    /// </summary>
    public PerformanceThresholds Performance { get; set; } = new();

    /// <summary>
    /// Cache configuration settings
    /// </summary>
    public CacheConfiguration Cache { get; set; } = new();

    /// <summary>
    /// Security and encryption settings
    /// </summary>
    public SecurityConfiguration Security { get; set; } = new();

    /// <summary>
    /// Monitoring and logging configuration
    /// </summary>
    public MonitoringConfiguration Monitoring { get; set; } = new();

    /// <summary>
    /// Data archival and cleanup settings
    /// </summary>
    public ArchivalConfiguration Archival { get; set; } = new();

    /// <summary>
    /// Validates the configuration settings
    /// </summary>
    /// <returns>List of validation errors</returns>
    public IEnumerable<string> Validate()
    {
        var errors = new List<string>();

        if (HotTierRetentionDays >= WarmTierRetentionDays)
        {
            errors.Add("HotTierRetentionDays must be less than WarmTierRetentionDays");
        }

        if (string.IsNullOrWhiteSpace(ConnectionStrings.Hot))
        {
            errors.Add("Hot tier connection string is required");
        }

        if (string.IsNullOrWhiteSpace(ConnectionStrings.Warm))
        {
            errors.Add("Warm tier connection string is required");
        }

        if (string.IsNullOrWhiteSpace(ConnectionStrings.Cold))
        {
            errors.Add("Cold tier connection string is required");
        }

        if (InitialRetryDelayMs >= MaxRetryDelayMs)
        {
            errors.Add("InitialRetryDelayMs must be less than MaxRetryDelayMs");
        }

        // Validate nested configurations
        errors.AddRange(Performance.Validate());
        errors.AddRange(Cache.Validate());
        errors.AddRange(Security.Validate());
        errors.AddRange(Monitoring.Validate());
        errors.AddRange(Archival.Validate());

        return errors;
    }
}

/// <summary>
/// Connection strings for different storage tiers
/// </summary>
public class ConnectionStrings
{
    /// <summary>
    /// Connection string for hot tier (SQL Server)
    /// </summary>
    [Required]
    public string Hot { get; set; } = string.Empty;

    /// <summary>
    /// Connection string for warm tier (SQL Server with compression)
    /// </summary>
    [Required]
    public string Warm { get; set; } = string.Empty;

    /// <summary>
    /// Connection string for cold tier (Azure Blob Storage)
    /// </summary>
    [Required]
    public string Cold { get; set; } = string.Empty;

    /// <summary>
    /// Redis connection string for caching
    /// </summary>
    public string? Redis { get; set; }
}

/// <summary>
/// Performance SLA thresholds
/// </summary>
public class PerformanceThresholds
{
    /// <summary>
    /// Maximum acceptable response time for hot tier queries (milliseconds)
    /// </summary>
    [Range(1, 10000)]
    public int HotTierMaxResponseTimeMs { get; set; } = 50;

    /// <summary>
    /// Maximum acceptable response time for warm tier queries (milliseconds)
    /// </summary>
    [Range(1, 10000)]
    public int WarmTierMaxResponseTimeMs { get; set; } = 200;

    /// <summary>
    /// Maximum acceptable response time for cold tier queries (milliseconds)
    /// </summary>
    [Range(1, 30000)]
    public int ColdTierMaxResponseTimeMs { get; set; } = 1000;

    /// <summary>
    /// Maximum memory usage per query (MB)
    /// </summary>
    [Range(1, 10240)]
    public int MaxMemoryUsageMb { get; set; } = 512;

    /// <summary>
    /// Maximum CPU usage percentage
    /// </summary>
    [Range(1, 100)]
    public int MaxCpuUsagePercent { get; set; } = 70;

    /// <summary>
    /// Validates performance threshold settings
    /// </summary>
    public IEnumerable<string> Validate()
    {
        var errors = new List<string>();

        if (HotTierMaxResponseTimeMs >= WarmTierMaxResponseTimeMs)
        {
            errors.Add("HotTierMaxResponseTimeMs must be less than WarmTierMaxResponseTimeMs");
        }

        if (WarmTierMaxResponseTimeMs >= ColdTierMaxResponseTimeMs)
        {
            errors.Add("WarmTierMaxResponseTimeMs must be less than ColdTierMaxResponseTimeMs");
        }

        return errors;
    }
}

/// <summary>
/// Cache configuration settings
/// </summary>
public class CacheConfiguration
{
    /// <summary>
    /// Enable or disable caching
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Default cache expiration in minutes
    /// </summary>
    [Range(1, 1440)]
    public int DefaultExpirationMinutes { get; set; } = 60;

    /// <summary>
    /// Maximum cache size in MB
    /// </summary>
    [Range(1, 10240)]
    public int MaxCacheSizeMb { get; set; } = 1024;

    /// <summary>
    /// Cache key prefix for namespacing
    /// </summary>
    public string KeyPrefix { get; set; } = "crm:history:";

    /// <summary>
    /// Enable cache compression
    /// </summary>
    public bool EnableCompression { get; set; } = true;

    /// <summary>
    /// Cache warmup queries on startup
    /// </summary>
    public bool WarmupOnStartup { get; set; } = true;

    /// <summary>
    /// Validates cache configuration
    /// </summary>
    public IEnumerable<string> Validate()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(KeyPrefix))
        {
            errors.Add("Cache KeyPrefix cannot be empty");
        }

        return errors;
    }
}

/// <summary>
/// Security and encryption configuration
/// </summary>
public class SecurityConfiguration
{
    /// <summary>
    /// Enable field value encryption for sensitive data
    /// </summary>
    public bool EnableFieldEncryption { get; set; } = true;

    /// <summary>
    /// Azure Key Vault URL for encryption keys
    /// </summary>
    public string? KeyVaultUrl { get; set; }

    /// <summary>
    /// Encryption key name in Key Vault
    /// </summary>
    public string? EncryptionKeyName { get; set; } = "crm-history-encryption-key";

    /// <summary>
    /// Enable audit logging for all operations
    /// </summary>
    public bool EnableAuditLogging { get; set; } = true;

    /// <summary>
    /// Fields that should be encrypted
    /// </summary>
    public IList<string> EncryptedFields { get; set; } = new List<string>
    {
        "SSN", "CreditCardNumber", "BankAccount", "PersonalNotes"
    };

    /// <summary>
    /// Validates security configuration
    /// </summary>
    public IEnumerable<string> Validate()
    {
        var errors = new List<string>();

        if (EnableFieldEncryption && string.IsNullOrWhiteSpace(KeyVaultUrl))
        {
            errors.Add("KeyVaultUrl is required when field encryption is enabled");
        }

        if (EnableFieldEncryption && string.IsNullOrWhiteSpace(EncryptionKeyName))
        {
            errors.Add("EncryptionKeyName is required when field encryption is enabled");
        }

        return errors;
    }
}

/// <summary>
/// Monitoring and logging configuration
/// </summary>
public class MonitoringConfiguration
{
    /// <summary>
    /// Enable performance monitoring
    /// </summary>
    public bool EnablePerformanceMonitoring { get; set; } = true;

    /// <summary>
    /// Enable detailed query logging
    /// </summary>
    public bool EnableQueryLogging { get; set; } = true;

    /// <summary>
    /// Log slow queries that exceed threshold (milliseconds)
    /// </summary>
    [Range(1, 60000)]
    public int SlowQueryThresholdMs { get; set; } = 1000;

    /// <summary>
    /// Application Insights instrumentation key
    /// </summary>
    public string? ApplicationInsightsKey { get; set; }

    /// <summary>
    /// Enable health checks
    /// </summary>
    public bool EnableHealthChecks { get; set; } = true;

    /// <summary>
    /// Health check interval in minutes
    /// </summary>
    [Range(1, 60)]
    public int HealthCheckIntervalMinutes { get; set; } = 5;

    /// <summary>
    /// Validates monitoring configuration
    /// </summary>
    public IEnumerable<string> Validate()
    {
        return Enumerable.Empty<string>();
    }
}

/// <summary>
/// Data archival and cleanup configuration
/// </summary>
public class ArchivalConfiguration
{
    /// <summary>
    /// Enable automatic archival
    /// </summary>
    public bool EnableAutoArchival { get; set; } = true;

    /// <summary>
    /// Archival schedule (cron expression)
    /// </summary>
    public string ArchivalSchedule { get; set; } = "0 2 * * *"; // Daily at 2 AM

    /// <summary>
    /// Number of days to retain data before permanent deletion
    /// </summary>
    [Range(365, 3650)]
    public int PermanentDeletionDays { get; set; } = 2555; // 7 years

    /// <summary>
    /// Enable data compression during archival
    /// </summary>
    public bool EnableCompression { get; set; } = true;

    /// <summary>
    /// Batch size for archival operations
    /// </summary>
    [Range(100, 10000)]
    public int ArchivalBatchSize { get; set; } = 5000;

    /// <summary>
    /// Validates archival configuration
    /// </summary>
    public IEnumerable<string> Validate()
    {
        var errors = new List<string>();

        if (EnableAutoArchival && string.IsNullOrWhiteSpace(ArchivalSchedule))
        {
            errors.Add("ArchivalSchedule is required when auto archival is enabled");
        }

        return errors;
    }
}
