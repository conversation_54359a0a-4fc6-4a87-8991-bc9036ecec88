{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "History": {"HotTierRetentionDays": 90, "WarmTierRetentionDays": 365, "BatchSize": 1000, "CacheExpirationMinutes": 60, "MaxConcurrentConnections": 100, "CommandTimeoutSeconds": 300, "MaxRetryAttempts": 3, "InitialRetryDelayMs": 100, "MaxRetryDelayMs": 5000, "ConnectionStrings": {"Hot": "Server=(localdb)\\mssqllocaldb;Database=CrmHistory_Hot;Trusted_Connection=true;MultipleActiveResultSets=true", "Warm": "Server=(localdb)\\mssqllocaldb;Database=CrmHistory_Warm;Trusted_Connection=true;MultipleActiveResultSets=true", "Cold": "DefaultEndpointsProtocol=https;AccountName=crmhistory;AccountKey=your_account_key;EndpointSuffix=core.windows.net", "Redis": "localhost:6379"}, "Performance": {"HotTierMaxResponseTimeMs": 50, "WarmTierMaxResponseTimeMs": 200, "ColdTierMaxResponseTimeMs": 1000, "MaxMemoryUsageMb": 512, "MaxCpuUsagePercent": 70}, "Cache": {"Enabled": true, "DefaultExpirationMinutes": 60, "MaxCacheSizeMb": 1024, "KeyPrefix": "crm:history:", "EnableCompression": true, "WarmupOnStartup": true}, "Security": {"EnableFieldEncryption": true, "KeyVaultUrl": "https://your-keyvault.vault.azure.net/", "EncryptionKeyName": "crm-history-encryption-key", "EnableAuditLogging": true, "EncryptedFields": ["SSN", "CreditCardNumber", "BankAccount", "PersonalNotes"]}, "Monitoring": {"EnablePerformanceMonitoring": true, "EnableQueryLogging": true, "SlowQueryThresholdMs": 1000, "ApplicationInsightsKey": "your_application_insights_key", "EnableHealthChecks": true, "HealthCheckIntervalMinutes": 5}, "Archival": {"EnableAutoArchival": true, "ArchivalSchedule": "0 2 * * *", "PermanentDeletionDays": 2555, "EnableCompression": true, "ArchivalBatchSize": 5000}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.ApplicationInsights"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "ApplicationInsights", "Args": {"instrumentationKey": "your_application_insights_key", "telemetryConverter": "Serilog.Sinks.ApplicationInsights.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"}}], "Enrich": ["FromLogContext", "WithEnvironment", "WithMachineName", "WithProcessId", "WithThreadId"], "Properties": {"Application": "CrmHistorySystem.Api"}}}