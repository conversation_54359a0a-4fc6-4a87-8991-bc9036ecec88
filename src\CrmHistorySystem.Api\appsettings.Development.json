{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft.AspNetCore": "Information", "Microsoft.EntityFrameworkCore": "Information"}}, "History": {"ConnectionStrings": {"Hot": "Server=(localdb)\\mssqllocaldb;Database=CrmHistory_Hot_Dev;Trusted_Connection=true;MultipleActiveResultSets=true", "Warm": "Server=(localdb)\\mssqllocaldb;Database=CrmHistory_Warm_Dev;Trusted_Connection=true;MultipleActiveResultSets=true", "Cold": "UseDevelopmentStorage=true", "Redis": "localhost:6379"}, "Security": {"EnableFieldEncryption": false}, "Monitoring": {"EnablePerformanceMonitoring": true, "EnableQueryLogging": true, "ApplicationInsightsKey": ""}}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information", "Microsoft.EntityFrameworkCore": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}]}}