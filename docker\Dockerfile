# CRM History System API Dockerfile
# Multi-stage build for optimized production image

# Build stage
FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /src

# Copy solution and project files
COPY ["CrmHistorySystem.sln", "./"]
COPY ["src/CrmHistorySystem.Core/CrmHistorySystem.Core.csproj", "src/CrmHistorySystem.Core/"]
COPY ["src/CrmHistorySystem.Infrastructure/CrmHistorySystem.Infrastructure.csproj", "src/CrmHistorySystem.Infrastructure/"]
COPY ["src/CrmHistorySystem.Api/CrmHistorySystem.Api.csproj", "src/CrmHistorySystem.Api/"]

# Restore dependencies
RUN dotnet restore "src/CrmHistorySystem.Api/CrmHistorySystem.Api.csproj"

# Copy source code
COPY . .

# Build the application
WORKDIR "/src/src/CrmHistorySystem.Api"
RUN dotnet build "CrmHistorySystem.Api.csproj" -c Release -o /app/build

# Publish stage
FROM build AS publish
RUN dotnet publish "CrmHistorySystem.Api.csproj" -c Release -o /app/publish /p:UseAppHost=false

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS final

# Create non-root user for security
RUN groupadd -r crmhistory && useradd -r -g crmhistory crmhistory

# Set working directory
WORKDIR /app

# Copy published application
COPY --from=publish /app/publish .

# Create directories for logs and data
RUN mkdir -p /app/logs /app/data && \
    chown -R crmhistory:crmhistory /app

# Install curl for health checks
RUN apt-get update && \
    apt-get install -y curl && \
    rm -rf /var/lib/apt/lists/*

# Switch to non-root user
USER crmhistory

# Expose ports
EXPOSE 80
EXPOSE 443

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:80
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENV DOTNET_USE_POLLING_FILE_WATCHER=true

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:80/health || exit 1

# Entry point
ENTRYPOINT ["dotnet", "CrmHistorySystem.Api.dll"]
