using CrmHistorySystem.Core.Models;

namespace CrmHistorySystem.Infrastructure.Data;

/// <summary>
/// Base interface for history repository operations across all storage tiers
/// </summary>
public interface IHistoryRepository
{
    /// <summary>
    /// Queries history entries based on the provided query parameters
    /// </summary>
    /// <param name="query">Query parameters</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Tuple containing entries and total count</returns>
    Task<(IEnumerable<HistoryEntry> entries, long totalCount)> QueryAsync(
        HistoryQuery query, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a single history entry
    /// </summary>
    /// <param name="entry">History entry to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successful</returns>
    Task<bool> AddAsync(HistoryEntry entry, CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds multiple history entries in batch
    /// </summary>
    /// <param name="entries">Collection of entries to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of entries successfully added</returns>
    Task<int> AddBatchAsync(IEnumerable<HistoryEntry> entries, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the total count of entries in this tier
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Total entry count</returns>
    Task<long> GetTotalCountAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets storage statistics for this tier
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Storage statistics</returns>
    Task<TierStorageStatistics> GetStorageStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates data integrity for entries in this tier
    /// </summary>
    /// <param name="sampleSize">Number of entries to validate</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Validation results</returns>
    Task<DataIntegrityResult> ValidateIntegrityAsync(int sampleSize, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for hot tier repository (SQL Server optimized for speed)
/// </summary>
public interface IHotTierRepository : IHistoryRepository
{
    /// <summary>
    /// Archives old entries to warm tier
    /// </summary>
    /// <param name="cutoffDate">Date before which entries should be archived</param>
    /// <param name="batchSize">Number of entries to process in each batch</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of entries archived</returns>
    Task<int> ArchiveToWarmTierAsync(DateTime cutoffDate, int batchSize, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets recent activity statistics
    /// </summary>
    /// <param name="hours">Number of hours to look back</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Activity statistics</returns>
    Task<ActivityStatistics> GetRecentActivityAsync(int hours = 24, CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for warm tier repository (SQL Server with compression)
/// </summary>
public interface IWarmTierRepository : IHistoryRepository
{
    /// <summary>
    /// Archives old entries to cold tier
    /// </summary>
    /// <param name="cutoffDate">Date before which entries should be archived</param>
    /// <param name="batchSize">Number of entries to process in each batch</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of entries archived</returns>
    Task<int> ArchiveToColdTierAsync(DateTime cutoffDate, int batchSize, CancellationToken cancellationToken = default);

    /// <summary>
    /// Compresses data to optimize storage
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Compression results</returns>
    Task<CompressionResult> CompressDataAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Interface for cold tier repository (Azure Blob Storage)
/// </summary>
public interface IColdTierRepository : IHistoryRepository
{
    /// <summary>
    /// Permanently deletes entries older than specified date
    /// </summary>
    /// <param name="cutoffDate">Date before which entries should be deleted</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of entries deleted</returns>
    Task<int> PermanentlyDeleteAsync(DateTime cutoffDate, CancellationToken cancellationToken = default);

    /// <summary>
    /// Exports entries to external storage for backup
    /// </summary>
    /// <param name="startDate">Start date for export</param>
    /// <param name="endDate">End date for export</param>
    /// <param name="exportPath">Path for export file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Export results</returns>
    Task<ExportResult> ExportDataAsync(DateTime startDate, DateTime endDate, string exportPath, CancellationToken cancellationToken = default);
}

/// <summary>
/// Storage statistics for a specific tier
/// </summary>
public class TierStorageStatistics
{
    public StorageTier Tier { get; set; }
    public long TotalEntries { get; set; }
    public long TotalSizeBytes { get; set; }
    public DateTime OldestEntry { get; set; }
    public DateTime NewestEntry { get; set; }
    public double AverageEntrySizeBytes { get; set; }
    public Dictionary<string, long> EntriesByField { get; set; } = new();
    public Dictionary<string, long> EntriesByUser { get; set; } = new();
    public double CompressionRatio { get; set; } = 1.0;
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Activity statistics for recent operations
/// </summary>
public class ActivityStatistics
{
    public int TotalWrites { get; set; }
    public int TotalReads { get; set; }
    public double AverageWriteTimeMs { get; set; }
    public double AverageReadTimeMs { get; set; }
    public Dictionary<string, int> WritesByField { get; set; } = new();
    public Dictionary<string, int> ReadsByField { get; set; } = new();
    public Dictionary<int, int> WritesByHour { get; set; } = new();
    public Dictionary<int, int> ReadsByHour { get; set; } = new();
    public DateTime PeriodStart { get; set; }
    public DateTime PeriodEnd { get; set; }
}

/// <summary>
/// Results of data compression operation
/// </summary>
public class CompressionResult
{
    public long OriginalSizeBytes { get; set; }
    public long CompressedSizeBytes { get; set; }
    public double CompressionRatio => OriginalSizeBytes > 0 ? (double)CompressedSizeBytes / OriginalSizeBytes : 1.0;
    public int EntriesCompressed { get; set; }
    public TimeSpan Duration { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime CompressedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Results of data export operation
/// </summary>
public class ExportResult
{
    public int EntriesExported { get; set; }
    public long FileSizeBytes { get; set; }
    public string ExportPath { get; set; } = string.Empty;
    public string Format { get; set; } = string.Empty;
    public TimeSpan Duration { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime ExportedAt { get; set; } = DateTime.UtcNow;
    public string? Checksum { get; set; }
}
