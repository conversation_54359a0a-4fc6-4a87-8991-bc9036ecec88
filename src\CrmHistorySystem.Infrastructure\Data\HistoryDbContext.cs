using CrmHistorySystem.Core.Models;
using Microsoft.EntityFrameworkCore;

namespace CrmHistorySystem.Infrastructure.Data;

/// <summary>
/// Entity Framework DbContext for hot and warm tier history storage
/// </summary>
public class HistoryDbContext : DbContext
{
    public HistoryDbContext(DbContextOptions<HistoryDbContext> options) : base(options)
    {
    }

    public DbSet<HistoryEntry> HistoryEntries { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure HistoryEntry entity
        modelBuilder.Entity<HistoryEntry>(entity =>
        {
            entity.ToTable("HistoryEntries_Hot");
            
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd();

            entity.Property(e => e.LeadId)
                .IsRequired();

            entity.Property(e => e.Field<PERSON>ame)
                .IsRequired()
                .HasMaxLength(100);

            entity.Property(e => e.OldValue)
                .HasMaxLength(4000);

            entity.Property(e => e.NewValue)
                .HasMaxLength(4000);

            entity.Property(e => e.ChangedAt)
                .IsRequired()
                .HasColumnType("datetime2");

            entity.Property(e => e.ChangedBy)
                .IsRequired()
                .HasMaxLength(100);

            entity.Property(e => e.Metadata)
                .HasMaxLength(8000);

            entity.Property(e => e.Tier)
                .HasConversion<int>();

            entity.Property(e => e.Checksum)
                .HasMaxLength(100);

            // Indexes for optimal query performance
            entity.HasIndex(e => new { e.LeadId, e.ChangedAt })
                .HasDatabaseName("IX_HistoryEntries_LeadId_ChangedAt")
                .IsClustered(false);

            entity.HasIndex(e => new { e.FieldName, e.ChangedAt })
                .HasDatabaseName("IX_HistoryEntries_FieldName_ChangedAt")
                .IsClustered(false);

            entity.HasIndex(e => e.ChangedAt)
                .HasDatabaseName("IX_HistoryEntries_ChangedAt")
                .IsClustered(false);

            entity.HasIndex(e => e.ChangedBy)
                .HasDatabaseName("IX_HistoryEntries_ChangedBy")
                .IsClustered(false);

            entity.HasIndex(e => e.Tier)
                .HasDatabaseName("IX_HistoryEntries_Tier")
                .IsClustered(false);
        });
    }
}

/// <summary>
/// Entity Framework DbContext for warm tier history storage with compression
/// </summary>
public class WarmTierDbContext : DbContext
{
    public WarmTierDbContext(DbContextOptions<WarmTierDbContext> options) : base(options)
    {
    }

    public DbSet<HistoryEntry> HistoryEntries { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure HistoryEntry entity for warm tier
        modelBuilder.Entity<HistoryEntry>(entity =>
        {
            entity.ToTable("HistoryEntries_Warm");
            
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd();

            entity.Property(e => e.LeadId)
                .IsRequired();

            entity.Property(e => e.FieldName)
                .IsRequired()
                .HasMaxLength(100);

            entity.Property(e => e.OldValue)
                .HasMaxLength(4000);

            entity.Property(e => e.NewValue)
                .HasMaxLength(4000);

            entity.Property(e => e.ChangedAt)
                .IsRequired()
                .HasColumnType("datetime2");

            entity.Property(e => e.ChangedBy)
                .IsRequired()
                .HasMaxLength(100);

            entity.Property(e => e.Metadata)
                .HasMaxLength(8000);

            entity.Property(e => e.Tier)
                .HasConversion<int>();

            entity.Property(e => e.Checksum)
                .HasMaxLength(100);

            // Indexes optimized for warm tier access patterns
            entity.HasIndex(e => new { e.LeadId, e.ChangedAt })
                .HasDatabaseName("IX_HistoryEntries_Warm_LeadId_ChangedAt")
                .IsClustered(false);

            entity.HasIndex(e => new { e.FieldName, e.ChangedAt })
                .HasDatabaseName("IX_HistoryEntries_Warm_FieldName_ChangedAt")
                .IsClustered(false);

            entity.HasIndex(e => e.ChangedAt)
                .HasDatabaseName("IX_HistoryEntries_Warm_ChangedAt")
                .IsClustered(false);

            // Enable data compression for warm tier
            entity.ToTable(tb => tb.HasComment("COMPRESSION = PAGE"));
        });
    }
}

/// <summary>
/// Base repository class with common Entity Framework operations
/// </summary>
public abstract class BaseEfRepository<TContext> where TContext : DbContext
{
    protected readonly TContext Context;
    protected readonly ILogger Logger;

    protected BaseEfRepository(TContext context, ILogger logger)
    {
        Context = context ?? throw new ArgumentNullException(nameof(context));
        Logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Builds a query based on HistoryQuery parameters
    /// </summary>
    protected IQueryable<HistoryEntry> BuildQuery(HistoryQuery query)
    {
        var queryable = Context.Set<HistoryEntry>().AsQueryable();

        // Apply filters
        if (query.LeadId.HasValue)
            queryable = queryable.Where(e => e.LeadId == query.LeadId.Value);

        if (!string.IsNullOrWhiteSpace(query.FieldName))
            queryable = queryable.Where(e => e.FieldName == query.FieldName);

        if (query.FieldNames != null && query.FieldNames.Any())
            queryable = queryable.Where(e => query.FieldNames.Contains(e.FieldName));

        if (!string.IsNullOrWhiteSpace(query.ChangedBy))
            queryable = queryable.Where(e => e.ChangedBy == query.ChangedBy);

        if (query.StartDate.HasValue)
            queryable = queryable.Where(e => e.ChangedAt >= query.StartDate.Value);

        if (query.EndDate.HasValue)
            queryable = queryable.Where(e => e.ChangedAt <= query.EndDate.Value);

        // Apply sorting
        queryable = query.SortBy switch
        {
            SortField.ChangedAt => query.SortOrder == SortOrder.Ascending 
                ? queryable.OrderBy(e => e.ChangedAt) 
                : queryable.OrderByDescending(e => e.ChangedAt),
            SortField.LeadId => query.SortOrder == SortOrder.Ascending 
                ? queryable.OrderBy(e => e.LeadId) 
                : queryable.OrderByDescending(e => e.LeadId),
            SortField.FieldName => query.SortOrder == SortOrder.Ascending 
                ? queryable.OrderBy(e => e.FieldName) 
                : queryable.OrderByDescending(e => e.FieldName),
            SortField.ChangedBy => query.SortOrder == SortOrder.Ascending 
                ? queryable.OrderBy(e => e.ChangedBy) 
                : queryable.OrderByDescending(e => e.ChangedBy),
            _ => queryable.OrderByDescending(e => e.ChangedAt)
        };

        return queryable;
    }

    /// <summary>
    /// Applies pagination to a query
    /// </summary>
    protected IQueryable<HistoryEntry> ApplyPagination(IQueryable<HistoryEntry> query, HistoryQuery historyQuery)
    {
        var skip = (historyQuery.Page - 1) * historyQuery.PageSize;
        var take = historyQuery.MaxResults.HasValue 
            ? Math.Min(historyQuery.PageSize, historyQuery.MaxResults.Value) 
            : historyQuery.PageSize;

        return query.Skip(skip).Take(take);
    }

    /// <summary>
    /// Executes a query with performance tracking
    /// </summary>
    protected async Task<(IEnumerable<HistoryEntry> entries, long totalCount)> ExecuteQueryAsync(
        HistoryQuery query, 
        CancellationToken cancellationToken)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            var baseQuery = BuildQuery(query);
            
            // Get total count
            var totalCount = await baseQuery.LongCountAsync(cancellationToken);
            
            // Apply pagination and execute
            var pagedQuery = ApplyPagination(baseQuery, query);
            var entries = await pagedQuery.ToListAsync(cancellationToken);

            Logger.LogDebug("Query executed in {Duration}ms, returned {Count} of {Total} entries",
                stopwatch.ElapsedMilliseconds, entries.Count, totalCount);

            return (entries, totalCount);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error executing query");
            throw;
        }
    }

    /// <summary>
    /// Adds entries in batch with transaction support
    /// </summary>
    protected async Task<int> AddBatchInternalAsync(
        IEnumerable<HistoryEntry> entries, 
        CancellationToken cancellationToken)
    {
        var entriesList = entries.ToList();
        if (!entriesList.Any())
            return 0;

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            using var transaction = await Context.Database.BeginTransactionAsync(cancellationToken);
            
            Context.Set<HistoryEntry>().AddRange(entriesList);
            var result = await Context.SaveChangesAsync(cancellationToken);
            
            await transaction.CommitAsync(cancellationToken);

            Logger.LogDebug("Batch insert of {Count} entries completed in {Duration}ms",
                entriesList.Count, stopwatch.ElapsedMilliseconds);

            return result;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during batch insert of {Count} entries", entriesList.Count);
            throw;
        }
    }
}
