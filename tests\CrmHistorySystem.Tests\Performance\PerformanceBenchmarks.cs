using CrmHistorySystem.Core.Models;
using NBomber.Contracts;
using NBomber.CSharp;
using System.Text;
using System.Text.Json;
using Xunit;
using Xunit.Abstractions;

namespace CrmHistorySystem.Tests.Performance;

/// <summary>
/// Performance benchmarks for the CRM History System
/// Tests SLA compliance and scalability requirements
/// </summary>
public class PerformanceBenchmarks
{
    private readonly ITestOutputHelper _output;
    private readonly HttpClient _httpClient;

    public PerformanceBenchmarks(ITestOutputHelper output)
    {
        _output = output;
        _httpClient = new HttpClient
        {
            BaseAddress = new Uri("https://localhost:7001") // Adjust based on your API URL
        };
    }

    [Fact]
    public async Task HotTier_QueryPerformance_ShouldMeetSlaRequirements()
    {
        // Arrange
        var scenario = Scenario.Create("hot_tier_query", async context =>
        {
            var query = new HistoryQuery
            {
                LeadId = Random.Shared.Next(1, 10000),
                StartDate = DateTime.UtcNow.AddDays(-30),
                EndDate = DateTime.UtcNow,
                PageSize = 100
            };

            var json = JsonSerializer.Serialize(query);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            using var response = await _httpClient.PostAsync("/api/v1/history/query", content);
            
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 100, during: TimeSpan.FromMinutes(2))
        );

        // Act
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert - Hot tier should respond within 50ms for 95th percentile
        var scnStats = stats.AllScenarios.First();
        _output.WriteLine($"Hot Tier Query Performance:");
        _output.WriteLine($"Mean Response Time: {scnStats.Ok.Response.Mean}ms");
        _output.WriteLine($"95th Percentile: {scnStats.Ok.Response.Percentile95}ms");
        _output.WriteLine($"99th Percentile: {scnStats.Ok.Response.Percentile99}ms");
        _output.WriteLine($"Success Rate: {scnStats.Ok.Request.Count}/{scnStats.AllRequestCount} ({(double)scnStats.Ok.Request.Count/scnStats.AllRequestCount*100:F2}%)");

        Assert.True(scnStats.Ok.Response.Percentile95 <= 50, 
            $"95th percentile response time ({scnStats.Ok.Response.Percentile95}ms) exceeds SLA requirement (50ms)");
        Assert.True((double)scnStats.Ok.Request.Count / scnStats.AllRequestCount >= 0.99, 
            "Success rate should be at least 99%");
    }

    [Fact]
    public async Task BatchInsert_Performance_ShouldHandle10000RecordsPerSecond()
    {
        // Arrange
        var scenario = Scenario.Create("batch_insert", async context =>
        {
            var entries = GenerateHistoryEntries(1000); // 1000 entries per batch
            var json = JsonSerializer.Serialize(entries);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            using var response = await _httpClient.PostAsync("/api/v1/history/batch", content);
            
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 10, during: TimeSpan.FromMinutes(1)) // 10 batches/sec = 10,000 records/sec
        );

        // Act
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert
        var scnStats = stats.AllScenarios.First();
        _output.WriteLine($"Batch Insert Performance:");
        _output.WriteLine($"Mean Response Time: {scnStats.Ok.Response.Mean}ms");
        _output.WriteLine($"95th Percentile: {scnStats.Ok.Response.Percentile95}ms");
        _output.WriteLine($"Throughput: {scnStats.Ok.Request.Count * 1000} records processed");
        _output.WriteLine($"Success Rate: {(double)scnStats.Ok.Request.Count/scnStats.AllRequestCount*100:F2}%");

        Assert.True(scnStats.Ok.Response.Percentile95 <= 5000, 
            $"95th percentile response time ({scnStats.Ok.Response.Percentile95}ms) exceeds acceptable threshold");
        Assert.True((double)scnStats.Ok.Request.Count / scnStats.AllRequestCount >= 0.95, 
            "Success rate should be at least 95% for batch operations");
    }

    [Fact]
    public async Task ConcurrentReads_Performance_ShouldHandle1000ConcurrentUsers()
    {
        // Arrange
        var scenario = Scenario.Create("concurrent_reads", async context =>
        {
            var leadId = Random.Shared.Next(1, 10000);
            using var response = await _httpClient.GetAsync($"/api/v1/history/lead/{leadId}?pageSize=50");
            
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.KeepConstant(copies: 1000, during: TimeSpan.FromMinutes(2))
        );

        // Act
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert
        var scnStats = stats.AllScenarios.First();
        _output.WriteLine($"Concurrent Reads Performance:");
        _output.WriteLine($"Mean Response Time: {scnStats.Ok.Response.Mean}ms");
        _output.WriteLine($"95th Percentile: {scnStats.Ok.Response.Percentile95}ms");
        _output.WriteLine($"Max Response Time: {scnStats.Ok.Response.Max}ms");
        _output.WriteLine($"RPS: {scnStats.Ok.Request.Count / 120.0:F2}"); // requests per second over 2 minutes
        _output.WriteLine($"Success Rate: {(double)scnStats.Ok.Request.Count/scnStats.AllRequestCount*100:F2}%");

        Assert.True(scnStats.Ok.Response.Percentile95 <= 200, 
            $"95th percentile response time ({scnStats.Ok.Response.Percentile95}ms) exceeds acceptable threshold for concurrent reads");
        Assert.True((double)scnStats.Ok.Request.Count / scnStats.AllRequestCount >= 0.99, 
            "Success rate should be at least 99% for read operations");
    }

    [Fact]
    public async Task MemoryUsage_UnderLoad_ShouldStayWithinLimits()
    {
        // Arrange
        var scenario = Scenario.Create("memory_stress", async context =>
        {
            // Mix of operations to stress memory usage
            var operation = Random.Shared.Next(1, 4);
            
            switch (operation)
            {
                case 1: // Query with large result set
                    var query = new HistoryQuery
                    {
                        StartDate = DateTime.UtcNow.AddDays(-90),
                        EndDate = DateTime.UtcNow,
                        PageSize = 1000
                    };
                    var json = JsonSerializer.Serialize(query);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");
                    using var response1 = await _httpClient.PostAsync("/api/v1/history/query", content);
                    return response1.IsSuccessStatusCode ? Response.Ok() : Response.Fail();

                case 2: // Field history query
                    var fieldName = $"Field{Random.Shared.Next(1, 100)}";
                    using var response2 = await _httpClient.GetAsync($"/api/v1/history/field/{fieldName}?pageSize=500");
                    return response2.IsSuccessStatusCode ? Response.Ok() : Response.Fail();

                case 3: // Statistics query
                    using var response3 = await _httpClient.GetAsync("/api/v1/history/statistics");
                    return response3.IsSuccessStatusCode ? Response.Ok() : Response.Fail();

                default:
                    return Response.Ok();
            }
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 50, during: TimeSpan.FromMinutes(3))
        );

        // Act
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert
        var scnStats = stats.AllScenarios.First();
        _output.WriteLine($"Memory Stress Test Performance:");
        _output.WriteLine($"Mean Response Time: {scnStats.Ok.Response.Mean}ms");
        _output.WriteLine($"95th Percentile: {scnStats.Ok.Response.Percentile95}ms");
        _output.WriteLine($"Success Rate: {(double)scnStats.Ok.Request.Count/scnStats.AllRequestCount*100:F2}%");

        // Memory usage should be monitored externally, but we can check response times don't degrade significantly
        Assert.True(scnStats.Ok.Response.Percentile95 <= 1000, 
            "Response times should not degrade significantly under memory stress");
        Assert.True((double)scnStats.Ok.Request.Count / scnStats.AllRequestCount >= 0.95, 
            "Success rate should remain high under memory stress");
    }

    [Fact]
    public async Task WarmTier_QueryPerformance_ShouldMeetSlaRequirements()
    {
        // Arrange - Query warm tier data (3-12 months old)
        var scenario = Scenario.Create("warm_tier_query", async context =>
        {
            var query = new HistoryQuery
            {
                LeadId = Random.Shared.Next(1, 10000),
                StartDate = DateTime.UtcNow.AddDays(-300), // 10 months ago
                EndDate = DateTime.UtcNow.AddDays(-100),   // 3+ months ago
                PageSize = 100
            };

            var json = JsonSerializer.Serialize(query);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            using var response = await _httpClient.PostAsync("/api/v1/history/query", content);
            
            return response.IsSuccessStatusCode ? Response.Ok() : Response.Fail();
        })
        .WithLoadSimulations(
            Simulation.InjectPerSec(rate: 50, during: TimeSpan.FromMinutes(2))
        );

        // Act
        var stats = NBomberRunner
            .RegisterScenarios(scenario)
            .Run();

        // Assert - Warm tier should respond within 200ms for 95th percentile
        var scnStats = stats.AllScenarios.First();
        _output.WriteLine($"Warm Tier Query Performance:");
        _output.WriteLine($"Mean Response Time: {scnStats.Ok.Response.Mean}ms");
        _output.WriteLine($"95th Percentile: {scnStats.Ok.Response.Percentile95}ms");
        _output.WriteLine($"Success Rate: {(double)scnStats.Ok.Request.Count/scnStats.AllRequestCount*100:F2}%");

        Assert.True(scnStats.Ok.Response.Percentile95 <= 200, 
            $"95th percentile response time ({scnStats.Ok.Response.Percentile95}ms) exceeds SLA requirement (200ms)");
    }

    private static List<HistoryEntry> GenerateHistoryEntries(int count)
    {
        var entries = new List<HistoryEntry>();
        var fields = new[] { "FirstName", "LastName", "Email", "Phone", "Status", "Company", "Title" };
        var users = new[] { "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" };

        for (int i = 0; i < count; i++)
        {
            entries.Add(new HistoryEntry
            {
                LeadId = Random.Shared.Next(1, 100000),
                FieldName = fields[Random.Shared.Next(fields.Length)],
                OldValue = $"OldValue{i}",
                NewValue = $"NewValue{i}",
                ChangedAt = DateTime.UtcNow.AddMinutes(-Random.Shared.Next(0, 1440)), // Within last 24 hours
                ChangedBy = users[Random.Shared.Next(users.Length)],
                Metadata = $"{{\"batchId\":\"{Guid.NewGuid()}\",\"source\":\"performance_test\"}}"
            });
        }

        return entries;
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}
