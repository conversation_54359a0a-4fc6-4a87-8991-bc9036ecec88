using CrmHistorySystem.Core.Models;
using FluentValidation;

namespace CrmHistorySystem.Core.Validation;

/// <summary>
/// Validator for HistoryEntry using FluentValidation.
/// Enforces data integrity and business rules for history entries.
/// </summary>
public class HistoryEntryValidator : AbstractValidator<HistoryEntry>
{
    public HistoryEntryValidator()
    {
        RuleFor(x => x.LeadId)
            .GreaterThan(0)
            .WithMessage("LeadId must be greater than 0")
            .WithErrorCode("INVALID_LEAD_ID");

        RuleFor(x => x.FieldName)
            .NotEmpty()
            .WithMessage("FieldName is required")
            .WithErrorCode("MISSING_FIELD_NAME")
            .MaximumLength(100)
            .WithMessage("FieldName cannot exceed 100 characters")
            .WithErrorCode("FIELD_NAME_TOO_LONG");

        RuleFor(x => x.ChangedBy)
            .NotEmpty()
            .WithMessage("ChangedBy is required")
            .WithErrorCode("MISSING_CHANGED_BY")
            .MaximumLength(100)
            .WithMessage("ChangedBy cannot exceed 100 characters")
            .WithErrorCode("CHANGED_BY_TOO_LONG");

        RuleFor(x => x.ChangedAt)
            .NotEqual(default(DateTime))
            .WithMessage("ChangedAt is required")
            .WithErrorCode("MISSING_CHANGED_AT")
            .LessThanOrEqualTo(DateTime.UtcNow.AddMinutes(5))
            .WithMessage("ChangedAt cannot be in the future")
            .WithErrorCode("FUTURE_CHANGE_DATE");

        // Optional fields validation
        When(x => x.OldValue != null, () =>
        {
            RuleFor(x => x.OldValue)
                .MaximumLength(4000)
                .WithMessage("OldValue cannot exceed 4000 characters")
                .WithErrorCode("OLD_VALUE_TOO_LONG");
        });

        When(x => x.NewValue != null, () =>
        {
            RuleFor(x => x.NewValue)
                .MaximumLength(4000)
                .WithMessage("NewValue cannot exceed 4000 characters")
                .WithErrorCode("NEW_VALUE_TOO_LONG");
        });

        When(x => x.Metadata != null, () =>
        {
            RuleFor(x => x.Metadata)
                .MaximumLength(8000)
                .WithMessage("Metadata cannot exceed 8000 characters")
                .WithErrorCode("METADATA_TOO_LONG")
                .Must(BeValidJson)
                .WithMessage("Metadata must be valid JSON")
                .WithErrorCode("INVALID_METADATA_JSON");
        });

        // Business rules
        RuleFor(x => x)
            .Must(HaveValueChange)
            .WithMessage("Either OldValue or NewValue must be provided")
            .WithErrorCode("NO_VALUE_CHANGE");
    }

    /// <summary>
    /// Validates that the string is valid JSON
    /// </summary>
    private bool BeValidJson(string? json)
    {
        if (string.IsNullOrWhiteSpace(json))
            return true;

        try
        {
            System.Text.Json.JsonDocument.Parse(json);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Validates that there is an actual value change
    /// </summary>
    private bool HaveValueChange(HistoryEntry entry)
    {
        return !string.IsNullOrWhiteSpace(entry.OldValue) || 
               !string.IsNullOrWhiteSpace(entry.NewValue);
    }
}
