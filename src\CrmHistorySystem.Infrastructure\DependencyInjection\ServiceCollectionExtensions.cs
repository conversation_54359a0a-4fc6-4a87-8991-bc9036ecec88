using CrmHistorySystem.Core.Configuration;
using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Infrastructure.Caching;
using CrmHistorySystem.Infrastructure.Data;
using CrmHistorySystem.Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Polly;
using Polly.Extensions.Http;
using StackExchange.Redis;

namespace CrmHistorySystem.Infrastructure.DependencyInjection;

/// <summary>
/// Extension methods for configuring CRM History System services in dependency injection container
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// Adds all CRM History System services to the dependency injection container
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <param name="configuration">Configuration</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddCrmHistorySystem(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        // Configure options
        services.Configure<HistoryOptions>(configuration.GetSection(HistoryOptions.SectionName));

        // Add core services
        services.AddHistoryServices();
        services.AddHistoryRepositories(configuration);
        services.AddHistoryCache(configuration);
        services.AddHistoryDatabase(configuration);

        return services;
    }

    /// <summary>
    /// Adds core history services
    /// </summary>
    private static IServiceCollection AddHistoryServices(this IServiceCollection services)
    {
        services.AddScoped<IHistoryService, TieredHistoryService>();
        
        return services;
    }

    /// <summary>
    /// Adds history repository implementations
    /// </summary>
    private static IServiceCollection AddHistoryRepositories(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        services.AddScoped<IHotTierRepository, HotTierRepository>();
        
        // TODO: Add warm and cold tier repositories when implemented
        // services.AddScoped<IWarmTierRepository, WarmTierRepository>();
        // services.AddScoped<IColdTierRepository, ColdTierRepository>();

        return services;
    }

    /// <summary>
    /// Adds Redis cache configuration
    /// </summary>
    private static IServiceCollection AddHistoryCache(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        var historyOptions = configuration.GetSection(HistoryOptions.SectionName).Get<HistoryOptions>();
        
        if (historyOptions?.Cache.Enabled == true && 
            !string.IsNullOrWhiteSpace(historyOptions.ConnectionStrings.Redis))
        {
            // Configure Redis connection
            services.AddSingleton<IConnectionMultiplexer>(provider =>
            {
                var logger = provider.GetRequiredService<ILogger<IConnectionMultiplexer>>();
                
                try
                {
                    var configuration = ConfigurationOptions.Parse(historyOptions.ConnectionStrings.Redis);
                    configuration.AbortOnConnectFail = false;
                    configuration.ConnectRetry = 3;
                    configuration.ConnectTimeout = 5000;
                    configuration.SyncTimeout = 5000;
                    
                    var multiplexer = ConnectionMultiplexer.Connect(configuration);
                    
                    multiplexer.ConnectionFailed += (sender, args) =>
                    {
                        logger.LogError("Redis connection failed: {EndPoint} - {FailureType}", 
                            args.EndPoint, args.FailureType);
                    };
                    
                    multiplexer.ConnectionRestored += (sender, args) =>
                    {
                        logger.LogInformation("Redis connection restored: {EndPoint}", args.EndPoint);
                    };
                    
                    return multiplexer;
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Failed to connect to Redis");
                    throw;
                }
            });

            services.AddScoped<IHistoryCache, RedisHistoryCache>();
        }
        else
        {
            // Use in-memory cache as fallback
            services.AddMemoryCache();
            // services.AddScoped<IHistoryCache, InMemoryHistoryCache>(); // TODO: Implement fallback cache
        }

        return services;
    }

    /// <summary>
    /// Adds Entity Framework database contexts
    /// </summary>
    private static IServiceCollection AddHistoryDatabase(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        var historyOptions = configuration.GetSection(HistoryOptions.SectionName).Get<HistoryOptions>();
        
        if (historyOptions?.ConnectionStrings == null)
        {
            throw new InvalidOperationException("History connection strings are not configured");
        }

        // Hot tier database context
        services.AddDbContext<HistoryDbContext>(options =>
        {
            options.UseSqlServer(historyOptions.ConnectionStrings.Hot, sqlOptions =>
            {
                sqlOptions.CommandTimeout(historyOptions.CommandTimeoutSeconds);
                sqlOptions.EnableRetryOnFailure(
                    maxRetryCount: historyOptions.MaxRetryAttempts,
                    maxRetryDelay: TimeSpan.FromMilliseconds(historyOptions.MaxRetryDelayMs),
                    errorNumbersToAdd: null);
            });

            options.EnableSensitiveDataLogging(false);
            options.EnableServiceProviderCaching();
            options.EnableDetailedErrors(false);
        });

        // Warm tier database context
        services.AddDbContext<WarmTierDbContext>(options =>
        {
            options.UseSqlServer(historyOptions.ConnectionStrings.Warm, sqlOptions =>
            {
                sqlOptions.CommandTimeout(historyOptions.CommandTimeoutSeconds);
                sqlOptions.EnableRetryOnFailure(
                    maxRetryCount: historyOptions.MaxRetryAttempts,
                    maxRetryDelay: TimeSpan.FromMilliseconds(historyOptions.MaxRetryDelayMs),
                    errorNumbersToAdd: null);
            });

            options.EnableSensitiveDataLogging(false);
            options.EnableServiceProviderCaching();
            options.EnableDetailedErrors(false);
        });

        return services;
    }

    /// <summary>
    /// Adds HTTP clients with Polly retry policies
    /// </summary>
    public static IServiceCollection AddHistoryHttpClients(this IServiceCollection services)
    {
        // Add HTTP client for external services with retry policy
        services.AddHttpClient("HistoryService", client =>
        {
            client.Timeout = TimeSpan.FromSeconds(30);
        })
        .AddPolicyHandler(GetRetryPolicy())
        .AddPolicyHandler(GetCircuitBreakerPolicy());

        return services;
    }

    /// <summary>
    /// Adds health checks for the history system
    /// </summary>
    public static IServiceCollection AddHistoryHealthChecks(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        var historyOptions = configuration.GetSection(HistoryOptions.SectionName).Get<HistoryOptions>();
        
        if (historyOptions?.Monitoring.EnableHealthChecks == true)
        {
            services.AddHealthChecks()
                .AddSqlServer(
                    historyOptions.ConnectionStrings.Hot,
                    name: "hot-tier-database",
                    tags: new[] { "database", "hot-tier" })
                .AddSqlServer(
                    historyOptions.ConnectionStrings.Warm,
                    name: "warm-tier-database",
                    tags: new[] { "database", "warm-tier" });

            if (!string.IsNullOrWhiteSpace(historyOptions.ConnectionStrings.Redis))
            {
                services.AddHealthChecks()
                    .AddRedis(
                        historyOptions.ConnectionStrings.Redis,
                        name: "redis-cache",
                        tags: new[] { "cache", "redis" });
            }

            // TODO: Add Azure Blob Storage health check for cold tier
        }

        return services;
    }

    /// <summary>
    /// Validates the configuration and throws if invalid
    /// </summary>
    public static IServiceCollection ValidateHistoryConfiguration(
        this IServiceCollection services, 
        IConfiguration configuration)
    {
        var historyOptions = configuration.GetSection(HistoryOptions.SectionName).Get<HistoryOptions>();
        
        if (historyOptions == null)
        {
            throw new InvalidOperationException("History configuration section is missing");
        }

        var validationErrors = historyOptions.Validate().ToList();
        
        if (validationErrors.Any())
        {
            var errorMessage = string.Join(Environment.NewLine, validationErrors);
            throw new InvalidOperationException($"History configuration is invalid:{Environment.NewLine}{errorMessage}");
        }

        return services;
    }

    /// <summary>
    /// Gets the retry policy for HTTP clients
    /// </summary>
    private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .WaitAndRetryAsync(
                retryCount: 3,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    var logger = context.GetLogger();
                    logger?.LogWarning("HTTP retry attempt {RetryCount} after {Delay}ms", 
                        retryCount, timespan.TotalMilliseconds);
                });
    }

    /// <summary>
    /// Gets the circuit breaker policy for HTTP clients
    /// </summary>
    private static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy()
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: 5,
                durationOfBreak: TimeSpan.FromSeconds(30),
                onBreak: (exception, duration) =>
                {
                    // Log circuit breaker opening
                },
                onReset: () =>
                {
                    // Log circuit breaker closing
                });
    }
}

/// <summary>
/// Extension methods for Polly context
/// </summary>
internal static class PollyContextExtensions
{
    private const string LoggerKey = "ILogger";

    public static Context WithLogger(this Context context, ILogger logger)
    {
        context[LoggerKey] = logger;
        return context;
    }

    public static ILogger? GetLogger(this Context context)
    {
        return context.TryGetValue(LoggerKey, out var logger) ? logger as ILogger : null;
    }
}
