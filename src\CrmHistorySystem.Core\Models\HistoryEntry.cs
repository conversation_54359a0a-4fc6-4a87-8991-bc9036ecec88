using System.ComponentModel.DataAnnotations;

namespace CrmHistorySystem.Core.Models;

/// <summary>
/// Represents a single field change history entry in the CRM system.
/// Optimized for high-volume storage and fast querying across tiered storage.
/// </summary>
public class HistoryEntry
{
    /// <summary>
    /// Unique identifier for the history entry
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// The ID of the lead that was modified
    /// </summary>
    [Required]
    public int LeadId { get; set; }

    /// <summary>
    /// Name of the field that was changed (e.g., "FirstName", "Email", "Status")
    /// </summary>
    [Required]
    [StringLength(100)]
    public string FieldName { get; set; } = string.Empty;

    /// <summary>
    /// The previous value of the field before the change
    /// </summary>
    public string? OldValue { get; set; }

    /// <summary>
    /// The new value of the field after the change
    /// </summary>
    public string? NewValue { get; set; }

    /// <summary>
    /// Timestamp when the change occurred (UTC)
    /// </summary>
    [Required]
    public DateTime ChangedAt { get; set; }

    /// <summary>
    /// Identifier of the user who made the change
    /// </summary>
    [Required]
    [StringLength(100)]
    public string ChangedBy { get; set; } = string.Empty;

    /// <summary>
    /// Optional metadata for additional context (JSON format)
    /// </summary>
    public string? Metadata { get; set; }

    /// <summary>
    /// Indicates which storage tier this entry belongs to
    /// </summary>
    public StorageTier Tier { get; set; } = StorageTier.Hot;

    /// <summary>
    /// Checksum for data integrity verification
    /// </summary>
    public string? Checksum { get; set; }

    /// <summary>
    /// Determines the appropriate storage tier based on the change date
    /// </summary>
    /// <param name="hotTierDays">Number of days for hot tier retention</param>
    /// <param name="warmTierDays">Number of days for warm tier retention</param>
    /// <returns>The appropriate storage tier</returns>
    public StorageTier DetermineStorageTier(int hotTierDays = 90, int warmTierDays = 365)
    {
        var daysSinceChange = (DateTime.UtcNow - ChangedAt).Days;
        
        if (daysSinceChange <= hotTierDays)
            return StorageTier.Hot;
        
        if (daysSinceChange <= warmTierDays)
            return StorageTier.Warm;
        
        return StorageTier.Cold;
    }

    /// <summary>
    /// Generates a checksum for data integrity verification
    /// </summary>
    /// <returns>SHA256 hash of the entry data</returns>
    public string GenerateChecksum()
    {
        var data = $"{LeadId}|{FieldName}|{OldValue}|{NewValue}|{ChangedAt:O}|{ChangedBy}";
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hash = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(data));
        return Convert.ToBase64String(hash);
    }
}

/// <summary>
/// Defines the storage tiers for history entries
/// </summary>
public enum StorageTier
{
    /// <summary>
    /// Hot tier: Recent data (0-3 months) stored in optimized SQL Server tables
    /// </summary>
    Hot = 1,

    /// <summary>
    /// Warm tier: Medium-term data (3-12 months) stored in compressed SQL Server tables
    /// </summary>
    Warm = 2,

    /// <summary>
    /// Cold tier: Long-term data (12+ months) stored in Azure Blob Storage
    /// </summary>
    Cold = 3
}
