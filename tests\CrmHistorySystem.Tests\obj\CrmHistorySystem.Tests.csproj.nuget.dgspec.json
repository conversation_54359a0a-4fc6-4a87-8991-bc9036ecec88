{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\tests\\CrmHistorySystem.Tests\\CrmHistorySystem.Tests.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Api\\CrmHistorySystem.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Api\\CrmHistorySystem.Api.csproj", "projectName": "CrmHistorySystem.Api", "projectPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Api\\CrmHistorySystem.Api.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Api\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\CrmHistorySystem.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\CrmHistorySystem.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\CrmHistorySystem.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\CrmHistorySystem.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AspNetCore.HealthChecks.Redis": {"target": "Package", "version": "[6.0.4, )"}, "AspNetCore.HealthChecks.SqlServer": {"target": "Package", "version": "[6.0.2, )"}, "AspNetCore.HealthChecks.UI": {"target": "Package", "version": "[6.0.5, )"}, "AspNetCore.HealthChecks.UI.Client": {"target": "Package", "version": "[6.0.5, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.0, )"}, "Microsoft.ApplicationInsights.AspNetCore": {"target": "Package", "version": "[2.21.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[6.0.24, )"}, "Microsoft.AspNetCore.Mvc.Versioning": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"target": "Package", "version": "[6.0.24, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[6.1.0, )"}, "Serilog.Enrichers.Environment": {"target": "Package", "version": "[2.2.0, )"}, "Serilog.Enrichers.Process": {"target": "Package", "version": "[2.0.2, )"}, "Serilog.Enrichers.Thread": {"target": "Package", "version": "[3.1.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[3.4.0, )"}, "Serilog.Sinks.ApplicationInsights": {"target": "Package", "version": "[4.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[4.1.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.5.0, )"}, "System.Text.Json": {"target": "Package", "version": "[6.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\CrmHistorySystem.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\CrmHistorySystem.Core.csproj", "projectName": "CrmHistorySystem.Core", "projectPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\CrmHistorySystem.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"FluentValidation": {"target": "Package", "version": "[11.8.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[6.0.4, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\CrmHistorySystem.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\CrmHistorySystem.Infrastructure.csproj", "projectName": "CrmHistorySystem.Infrastructure", "projectPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\CrmHistorySystem.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\CrmHistorySystem.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\CrmHistorySystem.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Azure.Storage.Blobs": {"target": "Package", "version": "[12.18.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.24, )"}, "Microsoft.Extensions.Caching.StackExchangeRedis": {"target": "Package", "version": "[6.0.24, )"}, "Microsoft.Extensions.Configuration.Binder": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.1, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[6.0.24, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.Extensions.Options": {"target": "Package", "version": "[6.0.0, )"}, "Polly": {"target": "Package", "version": "[7.2.4, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.6.122, )"}, "System.Text.Json": {"target": "Package", "version": "[6.0.8, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\tests\\CrmHistorySystem.Tests\\CrmHistorySystem.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\tests\\CrmHistorySystem.Tests\\CrmHistorySystem.Tests.csproj", "projectName": "CrmHistorySystem.Tests", "projectPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\tests\\CrmHistorySystem.Tests\\CrmHistorySystem.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\tests\\CrmHistorySystem.Tests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Api\\CrmHistorySystem.Api.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Api\\CrmHistorySystem.Api.csproj"}, "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\CrmHistorySystem.Core.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\CrmHistorySystem.Core.csproj"}, "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\CrmHistorySystem.Infrastructure.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\CrmHistorySystem.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoFixture": {"target": "Package", "version": "[4.18.0, )"}, "AutoFixture.Xunit2": {"target": "Package", "version": "[4.18.0, )"}, "FluentAssertions": {"target": "Package", "version": "[6.12.0, )"}, "Microsoft.AspNetCore.Mvc.Testing": {"target": "Package", "version": "[6.0.24, )"}, "Microsoft.EntityFrameworkCore.InMemory": {"target": "Package", "version": "[6.0.24, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.8.0, )"}, "Moq": {"target": "Package", "version": "[4.20.69, )"}, "NBomber": {"target": "Package", "version": "[5.0.6, )"}, "Testcontainers": {"target": "Package", "version": "[3.6.0, )"}, "Testcontainers.Redis": {"target": "Package", "version": "[3.6.0, )"}, "Testcontainers.SqlServer": {"target": "Package", "version": "[3.6.0, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.0, )"}, "xunit": {"target": "Package", "version": "[2.4.2, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.4.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}