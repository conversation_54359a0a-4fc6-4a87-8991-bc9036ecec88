using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Core.Models;
using CrmHistorySystem.Core.Validation;
using FluentValidation;
using Microsoft.AspNetCore.Mvc;
using System.Diagnostics;

namespace CrmHistorySystem.Api.Controllers;

/// <summary>
/// API controller for CRM history operations with high-performance querying and bulk operations
/// </summary>
[ApiController]
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/[controller]")]
[Produces("application/json")]
public class HistoryController : ControllerBase
{
    private readonly IHistoryService _historyService;
    private readonly IValidator<HistoryQuery> _queryValidator;
    private readonly IValidator<HistoryEntry> _entryValidator;
    private readonly ILogger<HistoryController> _logger;

    public HistoryController(
        IHistoryService historyService,
        IValidator<HistoryQuery> queryValidator,
        IValidator<HistoryEntry> entryValidator,
        ILogger<HistoryController> logger)
    {
        _historyService = historyService ?? throw new ArgumentNullException(nameof(historyService));
        _queryValidator = queryValidator ?? throw new ArgumentNullException(nameof(queryValidator));
        _entryValidator = entryValidator ?? throw new ArgumentNullException(nameof(entryValidator));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Retrieves history entries based on query parameters with automatic tier routing
    /// </summary>
    /// <param name="query">Query parameters for filtering and pagination</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Paginated history results with performance metrics</returns>
    /// <response code="200">Returns the history entries</response>
    /// <response code="400">Invalid query parameters</response>
    /// <response code="500">Internal server error</response>
    [HttpPost("query")]
    [ProducesResponseType(typeof(HistoryResult<HistoryEntry>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status500InternalServerError)]
    public async Task<ActionResult<HistoryResult<HistoryEntry>>> QueryHistory(
        [FromBody] HistoryQuery query,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        
        try
        {
            _logger.LogInformation("Received history query for LeadId: {LeadId}, FieldName: {FieldName}", 
                query.LeadId, query.FieldName);

            // Validate query
            var validationResult = await _queryValidator.ValidateAsync(query, cancellationToken);
            if (!validationResult.IsValid)
            {
                return BadRequest(CreateValidationProblemDetails(validationResult));
            }

            // Execute query
            var result = await _historyService.GetHistoryAsync(query, cancellationToken);
            
            // Add API-level performance metrics
            result.Performance.ExecutionTime = stopwatch.Elapsed;
            
            _logger.LogInformation("History query completed in {Duration}ms, returned {Count} entries", 
                stopwatch.ElapsedMilliseconds, result.Data.Count());

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing history query");
            return StatusCode(500, "An error occurred while processing the request");
        }
    }

    /// <summary>
    /// Retrieves history for a specific lead with optimized caching
    /// </summary>
    /// <param name="leadId">Lead identifier</param>
    /// <param name="startDate">Optional start date filter</param>
    /// <param name="endDate">Optional end date filter</param>
    /// <param name="page">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 100)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>History entries for the specified lead</returns>
    [HttpGet("lead/{leadId:int}")]
    [ProducesResponseType(typeof(HistoryResult<HistoryEntry>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<HistoryResult<HistoryEntry>>> GetLeadHistory(
        int leadId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 100,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (leadId <= 0)
                return BadRequest("LeadId must be greater than 0");

            if (page <= 0)
                return BadRequest("Page must be greater than 0");

            if (pageSize <= 0 || pageSize > 10000)
                return BadRequest("PageSize must be between 1 and 10000");

            var result = await _historyService.GetLeadHistoryAsync(
                leadId, startDate, endDate, pageSize, page, cancellationToken);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving lead history for LeadId: {LeadId}", leadId);
            return StatusCode(500, "An error occurred while retrieving lead history");
        }
    }

    /// <summary>
    /// Retrieves history for a specific field across all leads
    /// </summary>
    /// <param name="fieldName">Field name</param>
    /// <param name="startDate">Optional start date filter</param>
    /// <param name="endDate">Optional end date filter</param>
    /// <param name="page">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 100)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>History entries for the specified field</returns>
    [HttpGet("field/{fieldName}")]
    [ProducesResponseType(typeof(HistoryResult<HistoryEntry>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<HistoryResult<HistoryEntry>>> GetFieldHistory(
        string fieldName,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 100,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(fieldName))
                return BadRequest("FieldName is required");

            if (page <= 0)
                return BadRequest("Page must be greater than 0");

            if (pageSize <= 0 || pageSize > 10000)
                return BadRequest("PageSize must be between 1 and 10000");

            var result = await _historyService.GetFieldHistoryAsync(
                fieldName, startDate, endDate, pageSize, page, cancellationToken);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving field history for FieldName: {FieldName}", fieldName);
            return StatusCode(500, "An error occurred while retrieving field history");
        }
    }

    /// <summary>
    /// Adds a single history entry
    /// </summary>
    /// <param name="entry">History entry to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success status</returns>
    [HttpPost("entry")]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(typeof(ValidationProblemDetails), StatusCodes.Status400BadRequest)]
    public async Task<ActionResult> AddHistoryEntry(
        [FromBody] HistoryEntry entry,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate entry
            var validationResult = await _entryValidator.ValidateAsync(entry, cancellationToken);
            if (!validationResult.IsValid)
            {
                return BadRequest(CreateValidationProblemDetails(validationResult));
            }

            var success = await _historyService.AddHistoryEntryAsync(entry, cancellationToken);
            
            if (success)
            {
                _logger.LogInformation("History entry added for LeadId: {LeadId}, Field: {FieldName}", 
                    entry.LeadId, entry.FieldName);
                return CreatedAtAction(nameof(GetLeadHistory), new { leadId = entry.LeadId }, entry);
            }
            else
            {
                return StatusCode(500, "Failed to add history entry");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding history entry for LeadId: {LeadId}", entry.LeadId);
            return StatusCode(500, "An error occurred while adding the history entry");
        }
    }

    /// <summary>
    /// Adds multiple history entries in batch
    /// </summary>
    /// <param name="entries">Collection of history entries to add</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Batch processing results</returns>
    [HttpPost("batch")]
    [ProducesResponseType(typeof(HistoryBatch), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<HistoryBatch>> AddHistoryBatch(
        [FromBody] IEnumerable<HistoryEntry> entries,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var entriesList = entries.ToList();
            
            if (!entriesList.Any())
                return BadRequest("At least one entry is required");

            if (entriesList.Count > 10000)
                return BadRequest("Batch size cannot exceed 10,000 entries");

            var batch = new HistoryBatch
            {
                Entries = entriesList,
                CreatedBy = User.Identity?.Name ?? "API"
            };

            var result = await _historyService.ProcessHistoryBatchAsync(batch, cancellationToken);
            
            _logger.LogInformation("Batch processed: {BatchId}, Success: {Success}, Failed: {Failed}", 
                result.BatchId, result.ProcessedCount, result.FailedCount);

            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing history batch");
            return StatusCode(500, "An error occurred while processing the batch");
        }
    }

    /// <summary>
    /// Gets aggregated statistics about history data
    /// </summary>
    /// <param name="startDate">Optional start date for statistics</param>
    /// <param name="endDate">Optional end date for statistics</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Statistical information about history data</returns>
    [HttpGet("statistics")]
    [ProducesResponseType(typeof(HistoryStatistics), StatusCodes.Status200OK)]
    public async Task<ActionResult<HistoryStatistics>> GetStatistics(
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var statistics = await _historyService.GetHistoryStatisticsAsync(startDate, endDate, cancellationToken);
            return Ok(statistics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving history statistics");
            return StatusCode(500, "An error occurred while retrieving statistics");
        }
    }

    /// <summary>
    /// Validates data integrity across all storage tiers
    /// </summary>
    /// <param name="sampleSize">Number of records to validate (0 for all)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Data integrity validation results</returns>
    [HttpPost("validate-integrity")]
    [ProducesResponseType(typeof(DataIntegrityResult), StatusCodes.Status200OK)]
    public async Task<ActionResult<DataIntegrityResult>> ValidateIntegrity(
        [FromQuery] int sampleSize = 1000,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (sampleSize < 0)
                return BadRequest("SampleSize cannot be negative");

            var result = await _historyService.ValidateDataIntegrityAsync(sampleSize, cancellationToken);
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating data integrity");
            return StatusCode(500, "An error occurred during integrity validation");
        }
    }

    /// <summary>
    /// Triggers archival of old entries to appropriate storage tiers
    /// </summary>
    /// <param name="cutoffDate">Date before which entries should be archived</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of entries archived</returns>
    [HttpPost("archive")]
    [ProducesResponseType(typeof(int), StatusCodes.Status200OK)]
    public async Task<ActionResult<int>> ArchiveOldEntries(
        [FromQuery] DateTime cutoffDate,
        CancellationToken cancellationToken = default)
    {
        try
        {
            if (cutoffDate >= DateTime.UtcNow)
                return BadRequest("CutoffDate must be in the past");

            var archivedCount = await _historyService.ArchiveOldEntriesAsync(cutoffDate, cancellationToken);
            
            _logger.LogInformation("Archived {Count} entries older than {CutoffDate}", 
                archivedCount, cutoffDate);

            return Ok(archivedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during archival process");
            return StatusCode(500, "An error occurred during the archival process");
        }
    }

    /// <summary>
    /// Creates validation problem details from FluentValidation result
    /// </summary>
    private ValidationProblemDetails CreateValidationProblemDetails(FluentValidation.Results.ValidationResult validationResult)
    {
        var problemDetails = new ValidationProblemDetails();
        
        foreach (var error in validationResult.Errors)
        {
            if (problemDetails.Errors.ContainsKey(error.PropertyName))
            {
                problemDetails.Errors[error.PropertyName] = problemDetails.Errors[error.PropertyName]
                    .Concat(new[] { error.ErrorMessage }).ToArray();
            }
            else
            {
                problemDetails.Errors.Add(error.PropertyName, new[] { error.ErrorMessage });
            }
        }

        return problemDetails;
    }
}
