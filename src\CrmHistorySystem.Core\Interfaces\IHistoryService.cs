using CrmHistorySystem.Core.Models;

namespace CrmHistorySystem.Core.Interfaces;

/// <summary>
/// Primary interface for CRM history operations with support for tiered storage,
/// high-performance querying, and bulk operations.
/// </summary>
public interface IHistoryService
{
    /// <summary>
    /// Retrieves history entries based on the specified query parameters.
    /// Automatically routes queries across appropriate storage tiers for optimal performance.
    /// </summary>
    /// <param name="query">Query parameters including filters, pagination, and sorting</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>Paginated result with performance metrics</returns>
    Task<HistoryResult<HistoryEntry>> GetHistoryAsync(
        HistoryQuery query, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds a single history entry to the appropriate storage tier.
    /// Includes automatic tier determination and data validation.
    /// </summary>
    /// <param name="entry">History entry to add</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>True if the entry was successfully added</returns>
    Task<bool> AddHistoryEntryAsync(
        HistoryEntry entry, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Adds multiple history entries in a single batch operation.
    /// Optimized for high-throughput scenarios with automatic batching and error handling.
    /// </summary>
    /// <param name="entries">Collection of history entries to add</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>True if all entries were successfully added</returns>
    Task<bool> AddHistoryBatchAsync(
        IEnumerable<HistoryEntry> entries, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Processes a history batch with detailed validation and error reporting.
    /// Provides comprehensive feedback on processing results and failures.
    /// </summary>
    /// <param name="batch">Batch to process</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>Updated batch with processing results</returns>
    Task<HistoryBatch> ProcessHistoryBatchAsync(
        HistoryBatch batch, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Archives old history entries by moving them to appropriate cold storage.
    /// Implements automatic data lifecycle management based on retention policies.
    /// </summary>
    /// <param name="cutoffDate">Date before which entries should be archived</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>Number of entries successfully archived</returns>
    Task<int> ArchiveOldEntriesAsync(
        DateTime cutoffDate, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves history for a specific lead with optimized caching.
    /// Commonly used operation with enhanced performance characteristics.
    /// </summary>
    /// <param name="leadId">ID of the lead</param>
    /// <param name="startDate">Optional start date filter</param>
    /// <param name="endDate">Optional end date filter</param>
    /// <param name="pageSize">Number of records per page</param>
    /// <param name="page">Page number (1-based)</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>History entries for the specified lead</returns>
    Task<HistoryResult<HistoryEntry>> GetLeadHistoryAsync(
        int leadId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageSize = 100,
        int page = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Retrieves history for a specific field across all leads.
    /// Useful for analyzing field usage patterns and change frequencies.
    /// </summary>
    /// <param name="fieldName">Name of the field</param>
    /// <param name="startDate">Optional start date filter</param>
    /// <param name="endDate">Optional end date filter</param>
    /// <param name="pageSize">Number of records per page</param>
    /// <param name="page">Page number (1-based)</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>History entries for the specified field</returns>
    Task<HistoryResult<HistoryEntry>> GetFieldHistoryAsync(
        string fieldName,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageSize = 100,
        int page = 1,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets aggregated statistics about history data.
    /// Provides insights into data volume, distribution, and usage patterns.
    /// </summary>
    /// <param name="startDate">Optional start date for statistics</param>
    /// <param name="endDate">Optional end date for statistics</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>Statistical information about history data</returns>
    Task<HistoryStatistics> GetHistoryStatisticsAsync(
        DateTime? startDate = null,
        DateTime? endDate = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validates data integrity across all storage tiers.
    /// Performs checksum verification and identifies any data corruption.
    /// </summary>
    /// <param name="sampleSize">Number of records to validate (0 for all)</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>Data integrity validation results</returns>
    Task<DataIntegrityResult> ValidateDataIntegrityAsync(
        int sampleSize = 1000,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Migrates data between storage tiers based on retention policies.
    /// Automatically moves data to appropriate tiers for cost optimization.
    /// </summary>
    /// <param name="dryRun">If true, returns migration plan without executing</param>
    /// <param name="cancellationToken">Cancellation token for async operation</param>
    /// <returns>Migration results or plan</returns>
    Task<TierMigrationResult> MigrateBetweenTiersAsync(
        bool dryRun = false,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Statistical information about history data
/// </summary>
public class HistoryStatistics
{
    public long TotalEntries { get; set; }
    public long HotTierEntries { get; set; }
    public long WarmTierEntries { get; set; }
    public long ColdTierEntries { get; set; }
    public Dictionary<string, long> EntriesByField { get; set; } = new();
    public Dictionary<string, long> EntriesByUser { get; set; } = new();
    public DateTime OldestEntry { get; set; }
    public DateTime NewestEntry { get; set; }
    public double AverageEntriesPerDay { get; set; }
    public long TotalStorageSize { get; set; }
    public Dictionary<StorageTier, long> StorageSizeByTier { get; set; } = new();
}

/// <summary>
/// Results of data integrity validation
/// </summary>
public class DataIntegrityResult
{
    public int TotalValidated { get; set; }
    public int CorruptedEntries { get; set; }
    public int MissingChecksums { get; set; }
    public List<DataIntegrityError> Errors { get; set; } = new();
    public bool IsValid => CorruptedEntries == 0;
    public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Data integrity error details
/// </summary>
public class DataIntegrityError
{
    public long EntryId { get; set; }
    public StorageTier Tier { get; set; }
    public string ErrorType { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string? ExpectedChecksum { get; set; }
    public string? ActualChecksum { get; set; }
}

/// <summary>
/// Results of tier migration operation
/// </summary>
public class TierMigrationResult
{
    public int HotToWarmMigrated { get; set; }
    public int WarmToColdMigrated { get; set; }
    public int TotalMigrated { get; set; }
    public List<string> Errors { get; set; } = new();
    public TimeSpan Duration { get; set; }
    public bool WasDryRun { get; set; }
    public DateTime ExecutedAt { get; set; } = DateTime.UtcNow;
}
