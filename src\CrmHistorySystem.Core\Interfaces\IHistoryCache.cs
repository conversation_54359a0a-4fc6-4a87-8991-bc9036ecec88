using CrmHistorySystem.Core.Models;

namespace CrmHistorySystem.Core.Interfaces;

/// <summary>
/// Interface for caching history query results to improve performance.
/// Supports Redis-based distributed caching with intelligent cache key generation.
/// </summary>
public interface IHistoryCache
{
    /// <summary>
    /// Retrieves cached query results if available.
    /// </summary>
    /// <typeparam name="T">Type of cached data</typeparam>
    /// <param name="cacheKey">Unique cache key</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cached data or null if not found</returns>
    Task<T?> GetAsync<T>(string cacheKey, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Stores query results in cache with specified expiration.
    /// </summary>
    /// <typeparam name="T">Type of data to cache</typeparam>
    /// <param name="cacheKey">Unique cache key</param>
    /// <param name="data">Data to cache</param>
    /// <param name="expiration">Cache expiration time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successfully cached</returns>
    Task<bool> SetAsync<T>(
        string cacheKey, 
        T data, 
        TimeSpan expiration, 
        CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Removes specific cache entries.
    /// </summary>
    /// <param name="cacheKey">Cache key to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if successfully removed</returns>
    Task<bool> RemoveAsync(string cacheKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Removes cache entries matching a pattern.
    /// Useful for invalidating related cache entries.
    /// </summary>
    /// <param name="pattern">Pattern to match (supports wildcards)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of entries removed</returns>
    Task<int> RemoveByPatternAsync(string pattern, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates a cache key for a history query.
    /// Creates consistent, collision-resistant keys based on query parameters.
    /// </summary>
    /// <param name="query">History query</param>
    /// <returns>Unique cache key</returns>
    string GenerateCacheKey(HistoryQuery query);

    /// <summary>
    /// Generates a cache key for lead-specific queries.
    /// </summary>
    /// <param name="leadId">Lead ID</param>
    /// <param name="startDate">Start date filter</param>
    /// <param name="endDate">End date filter</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Unique cache key</returns>
    string GenerateLeadCacheKey(int leadId, DateTime? startDate, DateTime? endDate, int page, int pageSize);

    /// <summary>
    /// Generates a cache key for field-specific queries.
    /// </summary>
    /// <param name="fieldName">Field name</param>
    /// <param name="startDate">Start date filter</param>
    /// <param name="endDate">End date filter</param>
    /// <param name="page">Page number</param>
    /// <param name="pageSize">Page size</param>
    /// <returns>Unique cache key</returns>
    string GenerateFieldCacheKey(string fieldName, DateTime? startDate, DateTime? endDate, int page, int pageSize);

    /// <summary>
    /// Invalidates cache entries related to a specific lead.
    /// Called when new history entries are added for a lead.
    /// </summary>
    /// <param name="leadId">Lead ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of cache entries invalidated</returns>
    Task<int> InvalidateLeadCacheAsync(int leadId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Invalidates cache entries related to a specific field.
    /// Called when new history entries are added for a field.
    /// </summary>
    /// <param name="fieldName">Field name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of cache entries invalidated</returns>
    Task<int> InvalidateFieldCacheAsync(string fieldName, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets cache statistics and health information.
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Cache statistics</returns>
    Task<CacheStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Warms up the cache with frequently accessed data.
    /// </summary>
    /// <param name="queries">Queries to pre-cache</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of queries successfully cached</returns>
    Task<int> WarmupCacheAsync(IEnumerable<HistoryQuery> queries, CancellationToken cancellationToken = default);

    /// <summary>
    /// Clears all cache entries (use with caution).
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if cache was cleared</returns>
    Task<bool> ClearAllAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Cache performance and health statistics
/// </summary>
public class CacheStatistics
{
    /// <summary>
    /// Total number of cache hits
    /// </summary>
    public long TotalHits { get; set; }

    /// <summary>
    /// Total number of cache misses
    /// </summary>
    public long TotalMisses { get; set; }

    /// <summary>
    /// Cache hit ratio as a percentage
    /// </summary>
    public double HitRatio => TotalHits + TotalMisses > 0 ? (double)TotalHits / (TotalHits + TotalMisses) * 100 : 0;

    /// <summary>
    /// Total number of cached entries
    /// </summary>
    public long TotalEntries { get; set; }

    /// <summary>
    /// Total memory usage in bytes
    /// </summary>
    public long MemoryUsage { get; set; }

    /// <summary>
    /// Average cache entry size in bytes
    /// </summary>
    public double AverageEntrySize => TotalEntries > 0 ? (double)MemoryUsage / TotalEntries : 0;

    /// <summary>
    /// Number of expired entries removed
    /// </summary>
    public long ExpiredEntries { get; set; }

    /// <summary>
    /// Number of evicted entries (due to memory pressure)
    /// </summary>
    public long EvictedEntries { get; set; }

    /// <summary>
    /// Cache uptime
    /// </summary>
    public TimeSpan Uptime { get; set; }

    /// <summary>
    /// Last reset timestamp
    /// </summary>
    public DateTime LastReset { get; set; }

    /// <summary>
    /// Connection status to cache server
    /// </summary>
    public bool IsConnected { get; set; }

    /// <summary>
    /// Cache server information
    /// </summary>
    public string ServerInfo { get; set; } = string.Empty;

    /// <summary>
    /// Most frequently accessed cache keys
    /// </summary>
    public Dictionary<string, long> TopKeys { get; set; } = new();

    /// <summary>
    /// Cache performance by operation type
    /// </summary>
    public Dictionary<string, CacheOperationStats> OperationStats { get; set; } = new();
}

/// <summary>
/// Statistics for specific cache operations
/// </summary>
public class CacheOperationStats
{
    public long Count { get; set; }
    public TimeSpan TotalDuration { get; set; }
    public TimeSpan AverageDuration => Count > 0 ? TimeSpan.FromTicks(TotalDuration.Ticks / Count) : TimeSpan.Zero;
    public TimeSpan MinDuration { get; set; } = TimeSpan.MaxValue;
    public TimeSpan MaxDuration { get; set; } = TimeSpan.MinValue;
    public long Errors { get; set; }
    public double ErrorRate => Count > 0 ? (double)Errors / Count * 100 : 0;
}
