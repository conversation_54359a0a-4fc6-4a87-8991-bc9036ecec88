-- =============================================
-- CRM History System - Data Migration Script
-- Migrates existing JSON-based history to new relational structure
-- Includes validation, rollback capabilities, and progress tracking
-- =============================================

USE [CrmHistory_Hot]
GO

-- Create migration tracking table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[MigrationOperations]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[MigrationOperations](
        [Id] [bigint] IDENTITY(1,1) NOT NULL,
        [MigrationName] [nvarchar](100) NOT NULL,
        [SourceTable] [nvarchar](100) NOT NULL,
        [TargetTable] [nvarchar](100) NOT NULL,
        [TotalRecords] [bigint] NOT NULL DEFAULT(0),
        [ProcessedRecords] [bigint] NOT NULL DEFAULT(0),
        [SuccessfulRecords] [bigint] NOT NULL DEFAULT(0),
        [FailedRecords] [bigint] NOT NULL DEFAULT(0),
        [Status] [int] NOT NULL DEFAULT(0), -- 0=Pending, 1=InProgress, 2=Completed, 3=Failed, 4=RolledBack
        [StartedAt] [datetime2](7) NULL,
        [CompletedAt] [datetime2](7) NULL,
        [ErrorMessage] [nvarchar](max) NULL,
        [ValidationChecksum] [nvarchar](100) NULL,
        [CreatedAt] [datetime2](7) NOT NULL DEFAULT(GETUTCDATE()),
        [CreatedBy] [nvarchar](100) NOT NULL DEFAULT(SYSTEM_USER),
        CONSTRAINT [PK_MigrationOperations] PRIMARY KEY CLUSTERED ([Id] ASC)
    ) ON [PRIMARY]
END
GO

-- Create migration error log table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[MigrationErrors]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[MigrationErrors](
        [Id] [bigint] IDENTITY(1,1) NOT NULL,
        [MigrationId] [bigint] NOT NULL,
        [SourceRecordId] [nvarchar](100) NULL,
        [ErrorType] [nvarchar](50) NOT NULL,
        [ErrorMessage] [nvarchar](max) NOT NULL,
        [SourceData] [nvarchar](max) NULL,
        [OccurredAt] [datetime2](7) NOT NULL DEFAULT(GETUTCDATE()),
        CONSTRAINT [PK_MigrationErrors] PRIMARY KEY CLUSTERED ([Id] ASC),
        CONSTRAINT [FK_MigrationErrors_MigrationOperations] FOREIGN KEY ([MigrationId]) 
            REFERENCES [dbo].[MigrationOperations]([Id])
    ) ON [PRIMARY]
END
GO

-- Create backup table for rollback capability
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[HistoryEntries_Backup]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[HistoryEntries_Backup](
        [Id] [bigint] IDENTITY(1,1) NOT NULL,
        [OriginalId] [bigint] NULL,
        [LeadId] [int] NOT NULL,
        [FieldName] [nvarchar](100) NOT NULL,
        [OldValue] [nvarchar](4000) NULL,
        [NewValue] [nvarchar](4000) NULL,
        [ChangedAt] [datetime2](7) NOT NULL,
        [ChangedBy] [nvarchar](100) NOT NULL,
        [Metadata] [nvarchar](8000) NULL,
        [Tier] [int] NOT NULL,
        [Checksum] [nvarchar](100) NULL,
        [BackupAt] [datetime2](7) NOT NULL DEFAULT(GETUTCDATE()),
        [MigrationId] [bigint] NOT NULL,
        CONSTRAINT [PK_HistoryEntries_Backup] PRIMARY KEY CLUSTERED ([Id] ASC)
    ) ON [PRIMARY]
END
GO

-- Create stored procedure for JSON to relational migration
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_MigrateJsonToRelational]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_MigrateJsonToRelational]
GO

CREATE PROCEDURE [dbo].[sp_MigrateJsonToRelational]
    @SourceTable NVARCHAR(100),
    @JsonColumn NVARCHAR(100),
    @BatchSize INT = 10000,
    @ValidateData BIT = 1,
    @CreateBackup BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @MigrationId BIGINT;
    DECLARE @TotalRecords BIGINT = 0;
    DECLARE @ProcessedRecords BIGINT = 0;
    DECLARE @SuccessfulRecords BIGINT = 0;
    DECLARE @FailedRecords BIGINT = 0;
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @SQL NVARCHAR(MAX);
    DECLARE @ValidationChecksum NVARCHAR(100);
    
    BEGIN TRY
        -- Create migration operation record
        INSERT INTO [dbo].[MigrationOperations] 
        ([MigrationName], [SourceTable], [TargetTable], [Status], [StartedAt])
        VALUES 
        ('JSON_TO_RELATIONAL', @SourceTable, 'HistoryEntries_Hot', 1, GETUTCDATE());
        
        SET @MigrationId = SCOPE_IDENTITY();
        
        -- Get total record count
        SET @SQL = N'SELECT @TotalRecords = COUNT(*) FROM ' + QUOTENAME(@SourceTable) + 
                   N' WHERE ' + QUOTENAME(@JsonColumn) + N' IS NOT NULL';
        EXEC sp_executesql @SQL, N'@TotalRecords BIGINT OUTPUT', @TotalRecords OUTPUT;
        
        UPDATE [dbo].[MigrationOperations] 
        SET [TotalRecords] = @TotalRecords 
        WHERE [Id] = @MigrationId;
        
        PRINT 'Starting migration of ' + CAST(@TotalRecords AS NVARCHAR(20)) + ' records...';
        
        -- Process records in batches
        WHILE @ProcessedRecords < @TotalRecords
        BEGIN
            DECLARE @BatchStart BIGINT = @ProcessedRecords;
            DECLARE @BatchEnd BIGINT = @ProcessedRecords + @BatchSize;
            DECLARE @CurrentBatchSize INT = 0;
            
            BEGIN TRANSACTION;
            
            -- Create temporary table for batch processing
            CREATE TABLE #BatchData (
                SourceId BIGINT,
                LeadId INT,
                FieldName NVARCHAR(100),
                OldValue NVARCHAR(4000),
                NewValue NVARCHAR(4000),
                ChangedAt DATETIME2,
                ChangedBy NVARCHAR(100),
                Metadata NVARCHAR(8000),
                JsonData NVARCHAR(MAX),
                IsValid BIT DEFAULT(1),
                ErrorMessage NVARCHAR(MAX)
            );
            
            -- Extract and parse JSON data for current batch
            SET @SQL = N'
            INSERT INTO #BatchData (SourceId, JsonData)
            SELECT Id, ' + QUOTENAME(@JsonColumn) + N'
            FROM (
                SELECT Id, ' + QUOTENAME(@JsonColumn) + N', 
                       ROW_NUMBER() OVER (ORDER BY Id) as RowNum
                FROM ' + QUOTENAME(@SourceTable) + N'
                WHERE ' + QUOTENAME(@JsonColumn) + N' IS NOT NULL
            ) t
            WHERE RowNum > @BatchStart AND RowNum <= @BatchEnd';
            
            EXEC sp_executesql @SQL, 
                N'@BatchStart BIGINT, @BatchEnd BIGINT', 
                @BatchStart, @BatchEnd;
            
            SET @CurrentBatchSize = @@ROWCOUNT;
            
            -- Parse JSON and validate data
            UPDATE #BatchData
            SET 
                LeadId = TRY_CAST(JSON_VALUE(JsonData, '$.LeadId') AS INT),
                FieldName = JSON_VALUE(JsonData, '$.FieldName'),
                OldValue = JSON_VALUE(JsonData, '$.OldValue'),
                NewValue = JSON_VALUE(JsonData, '$.NewValue'),
                ChangedAt = TRY_CAST(JSON_VALUE(JsonData, '$.ChangedAt') AS DATETIME2),
                ChangedBy = JSON_VALUE(JsonData, '$.ChangedBy'),
                Metadata = JSON_VALUE(JsonData, '$.Metadata');
            
            -- Validate parsed data
            UPDATE #BatchData
            SET 
                IsValid = 0,
                ErrorMessage = CASE 
                    WHEN LeadId IS NULL OR LeadId <= 0 THEN 'Invalid LeadId'
                    WHEN FieldName IS NULL OR LEN(FieldName) = 0 THEN 'Missing FieldName'
                    WHEN ChangedAt IS NULL THEN 'Invalid ChangedAt'
                    WHEN ChangedBy IS NULL OR LEN(ChangedBy) = 0 THEN 'Missing ChangedBy'
                    WHEN LEN(FieldName) > 100 THEN 'FieldName too long'
                    WHEN LEN(ChangedBy) > 100 THEN 'ChangedBy too long'
                    WHEN LEN(OldValue) > 4000 THEN 'OldValue too long'
                    WHEN LEN(NewValue) > 4000 THEN 'NewValue too long'
                    WHEN LEN(Metadata) > 8000 THEN 'Metadata too long'
                    ELSE 'Valid'
                END
            WHERE IsValid = 1;
            
            -- Log validation errors
            INSERT INTO [dbo].[MigrationErrors] ([MigrationId], [SourceRecordId], [ErrorType], [ErrorMessage], [SourceData])
            SELECT @MigrationId, CAST(SourceId AS NVARCHAR(100)), 'VALIDATION_ERROR', ErrorMessage, JsonData
            FROM #BatchData
            WHERE IsValid = 0;
            
            -- Create backup if requested
            IF @CreateBackup = 1
            BEGIN
                INSERT INTO [dbo].[HistoryEntries_Backup] 
                ([OriginalId], [LeadId], [FieldName], [OldValue], [NewValue], [ChangedAt], [ChangedBy], [Metadata], [Tier], [MigrationId])
                SELECT SourceId, LeadId, FieldName, OldValue, NewValue, ChangedAt, ChangedBy, Metadata, 1, @MigrationId
                FROM #BatchData
                WHERE IsValid = 1;
            END
            
            -- Insert valid records into target table
            INSERT INTO [dbo].[HistoryEntries_Hot] 
            ([LeadId], [FieldName], [OldValue], [NewValue], [ChangedAt], [ChangedBy], [Metadata], [Tier])
            SELECT LeadId, FieldName, OldValue, NewValue, ChangedAt, ChangedBy, Metadata, 1
            FROM #BatchData
            WHERE IsValid = 1;
            
            DECLARE @BatchSuccessful INT = @@ROWCOUNT;
            DECLARE @BatchFailed INT = @CurrentBatchSize - @BatchSuccessful;
            
            SET @SuccessfulRecords = @SuccessfulRecords + @BatchSuccessful;
            SET @FailedRecords = @FailedRecords + @BatchFailed;
            SET @ProcessedRecords = @ProcessedRecords + @CurrentBatchSize;
            
            -- Update progress
            UPDATE [dbo].[MigrationOperations] 
            SET 
                [ProcessedRecords] = @ProcessedRecords,
                [SuccessfulRecords] = @SuccessfulRecords,
                [FailedRecords] = @FailedRecords
            WHERE [Id] = @MigrationId;
            
            COMMIT TRANSACTION;
            
            DROP TABLE #BatchData;
            
            -- Progress reporting
            IF @ProcessedRecords % (@BatchSize * 10) = 0 OR @ProcessedRecords >= @TotalRecords
            BEGIN
                DECLARE @ProgressPct DECIMAL(5,2) = (@ProcessedRecords * 100.0) / @TotalRecords;
                PRINT 'Progress: ' + CAST(@ProgressPct AS NVARCHAR(10)) + '% (' + 
                      CAST(@ProcessedRecords AS NVARCHAR(20)) + '/' + CAST(@TotalRecords AS NVARCHAR(20)) + ')';
            END
        END
        
        -- Generate validation checksum
        SELECT @ValidationChecksum = CONVERT(NVARCHAR(100), HASHBYTES('SHA2_256', 
            CAST(@TotalRecords AS NVARCHAR(20)) + '|' + 
            CAST(@SuccessfulRecords AS NVARCHAR(20)) + '|' + 
            CAST(@FailedRecords AS NVARCHAR(20))), 2);
        
        -- Mark migration as completed
        UPDATE [dbo].[MigrationOperations] 
        SET 
            [Status] = 2, -- Completed
            [CompletedAt] = GETUTCDATE(),
            [ValidationChecksum] = @ValidationChecksum
        WHERE [Id] = @MigrationId;
        
        PRINT 'Migration completed successfully!';
        PRINT 'Total Records: ' + CAST(@TotalRecords AS NVARCHAR(20));
        PRINT 'Successful: ' + CAST(@SuccessfulRecords AS NVARCHAR(20));
        PRINT 'Failed: ' + CAST(@FailedRecords AS NVARCHAR(20));
        PRINT 'Validation Checksum: ' + @ValidationChecksum;
        
        SELECT 
            @MigrationId AS MigrationId,
            @TotalRecords AS TotalRecords,
            @SuccessfulRecords AS SuccessfulRecords,
            @FailedRecords AS FailedRecords,
            @ValidationChecksum AS ValidationChecksum;
            
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        SET @ErrorMessage = ERROR_MESSAGE();
        
        -- Mark migration as failed
        UPDATE [dbo].[MigrationOperations] 
        SET 
            [Status] = 3, -- Failed
            [CompletedAt] = GETUTCDATE(),
            [ErrorMessage] = @ErrorMessage
        WHERE [Id] = @MigrationId;
        
        -- Log the error
        INSERT INTO [dbo].[MigrationErrors] ([MigrationId], [ErrorType], [ErrorMessage])
        VALUES (@MigrationId, 'MIGRATION_FAILURE', @ErrorMessage);
        
        PRINT 'Migration failed: ' + @ErrorMessage;
        
        THROW;
    END CATCH
END
GO

-- Create stored procedure for rollback
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_RollbackMigration]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_RollbackMigration]
GO

CREATE PROCEDURE [dbo].[sp_RollbackMigration]
    @MigrationId BIGINT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ErrorMessage NVARCHAR(MAX);
    DECLARE @RolledBackCount INT = 0;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Verify migration exists and is completed
        IF NOT EXISTS (SELECT 1 FROM [dbo].[MigrationOperations] WHERE [Id] = @MigrationId AND [Status] = 2)
        BEGIN
            RAISERROR('Migration not found or not in completed status', 16, 1);
            RETURN;
        END
        
        -- Delete migrated records (those created after migration started)
        DECLARE @MigrationStartTime DATETIME2;
        SELECT @MigrationStartTime = [StartedAt] 
        FROM [dbo].[MigrationOperations] 
        WHERE [Id] = @MigrationId;
        
        DELETE FROM [dbo].[HistoryEntries_Hot]
        WHERE [CreatedAt] >= @MigrationStartTime;
        
        SET @RolledBackCount = @@ROWCOUNT;
        
        -- Mark migration as rolled back
        UPDATE [dbo].[MigrationOperations] 
        SET [Status] = 4 -- RolledBack
        WHERE [Id] = @MigrationId;
        
        COMMIT TRANSACTION;
        
        PRINT 'Rollback completed successfully!';
        PRINT 'Records removed: ' + CAST(@RolledBackCount AS NVARCHAR(20));
        
        SELECT @RolledBackCount AS RolledBackCount;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        SET @ErrorMessage = ERROR_MESSAGE();
        PRINT 'Rollback failed: ' + @ErrorMessage;
        
        THROW;
    END CATCH
END
GO

-- Create stored procedure for data validation
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_ValidateMigration]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_ValidateMigration]
GO

CREATE PROCEDURE [dbo].[sp_ValidateMigration]
    @MigrationId BIGINT,
    @SampleSize INT = 1000
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ValidationErrors INT = 0;
    DECLARE @SampleValidated INT = 0;
    DECLARE @ChecksumMatches INT = 0;
    
    -- Validate sample of migrated records
    WITH SampleRecords AS (
        SELECT TOP (@SampleSize) *
        FROM [dbo].[HistoryEntries_Hot] h
        INNER JOIN [dbo].[HistoryEntries_Backup] b ON h.Id = b.OriginalId
        WHERE b.MigrationId = @MigrationId
        ORDER BY NEWID()
    )
    SELECT 
        @SampleValidated = COUNT(*),
        @ValidationErrors = SUM(CASE 
            WHEN h.LeadId != b.LeadId THEN 1
            WHEN h.FieldName != b.FieldName THEN 1
            WHEN ISNULL(h.OldValue, '') != ISNULL(b.OldValue, '') THEN 1
            WHEN ISNULL(h.NewValue, '') != ISNULL(b.NewValue, '') THEN 1
            WHEN h.ChangedAt != b.ChangedAt THEN 1
            WHEN h.ChangedBy != b.ChangedBy THEN 1
            ELSE 0
        END)
    FROM SampleRecords h
    INNER JOIN [dbo].[HistoryEntries_Backup] b ON h.Id = b.OriginalId;
    
    -- Validate checksums if present
    SELECT @ChecksumMatches = COUNT(*)
    FROM [dbo].[HistoryEntries_Hot]
    WHERE [Checksum] IS NOT NULL 
      AND [Checksum] = dbo.fn_GenerateChecksum(LeadId, FieldName, OldValue, NewValue, ChangedAt, ChangedBy);
    
    SELECT 
        @MigrationId AS MigrationId,
        @SampleValidated AS SampleValidated,
        @ValidationErrors AS ValidationErrors,
        @ChecksumMatches AS ChecksumMatches,
        CASE WHEN @ValidationErrors = 0 THEN 'PASSED' ELSE 'FAILED' END AS ValidationResult;
END
GO

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON [dbo].[MigrationOperations] TO [CrmHistoryUser];
GRANT SELECT, INSERT ON [dbo].[MigrationErrors] TO [CrmHistoryUser];
GRANT SELECT, INSERT ON [dbo].[HistoryEntries_Backup] TO [CrmHistoryUser];
GRANT EXECUTE ON [dbo].[sp_MigrateJsonToRelational] TO [CrmHistoryUser];
GRANT EXECUTE ON [dbo].[sp_RollbackMigration] TO [CrmHistoryUser];
GRANT EXECUTE ON [dbo].[sp_ValidateMigration] TO [CrmHistoryUser];

PRINT 'Data migration scripts created successfully';
GO
