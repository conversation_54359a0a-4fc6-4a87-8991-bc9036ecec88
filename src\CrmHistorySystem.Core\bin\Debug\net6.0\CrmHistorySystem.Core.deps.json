{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"CrmHistorySystem.Core/1.0.0": {"dependencies": {"FluentValidation": "11.8.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.4", "System.ComponentModel.Annotations": "5.0.0"}, "runtime": {"CrmHistorySystem.Core.dll": {}}}, "FluentValidation/11.8.0": {"runtime": {"lib/net6.0/FluentValidation.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.8.0.0"}}}, "Microsoft.Extensions.Logging.Abstractions/6.0.4": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "6.0.0.0", "fileVersion": "6.0.1823.26907"}}}, "System.ComponentModel.Annotations/5.0.0": {}}}, "libraries": {"CrmHistorySystem.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "FluentValidation/11.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-TiEaaYqSIAS+wXQv46LRm8Mca092XYNePD0n231Hqlkmm5FuToZoWnjI1ef/vJUxBzqR7/k2vaVQXxijNgVKRQ==", "path": "fluentvalidation/11.8.0", "hashPath": "fluentvalidation.11.8.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-K14wYgwOfKVELrUh5eBqlC8Wvo9vvhS3ZhIvcswV2uS/ubkTRPSQsN557EZiYUSSoZNxizG+alN4wjtdyLdcyw==", "path": "microsoft.extensions.logging.abstractions/6.0.4", "hashPath": "microsoft.extensions.logging.abstractions.6.0.4.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}}}