using CrmHistorySystem.Core.Models;
using FluentValidation;

namespace CrmHistorySystem.Core.Validation;

/// <summary>
/// Validator for HistoryQuery using FluentValidation.
/// Ensures query parameters are valid and within acceptable limits.
/// </summary>
public class HistoryQueryValidator : AbstractValidator<HistoryQuery>
{
    public HistoryQueryValidator()
    {
        // LeadId validation
        When(x => x.LeadId.HasValue, () =>
        {
            RuleFor(x => x.LeadId)
                .GreaterThan(0)
                .WithMessage("LeadId must be greater than 0")
                .WithErrorCode("INVALID_LEAD_ID");
        });

        // FieldName validation
        When(x => !string.IsNullOrWhiteSpace(x.FieldName), () =>
        {
            RuleFor(x => x.FieldName)
                .MaximumLength(100)
                .WithMessage("FieldName cannot exceed 100 characters")
                .WithErrorCode("FIELD_NAME_TOO_LONG");
        });

        // FieldNames validation
        When(x => x.FieldNames != null && x.FieldNames.Any(), () =>
        {
            RuleFor(x => x.FieldNames)
                .Must(fieldNames => fieldNames.Count() <= 50)
                .WithMessage("Cannot query more than 50 fields at once")
                .WithErrorCode("TOO_MANY_FIELDS");

            RuleForEach(x => x.FieldNames)
                .NotEmpty()
                .WithMessage("Field names cannot be empty")
                .WithErrorCode("EMPTY_FIELD_NAME")
                .MaximumLength(100)
                .WithMessage("Field name cannot exceed 100 characters")
                .WithErrorCode("FIELD_NAME_TOO_LONG");
        });

        // ChangedBy validation
        When(x => !string.IsNullOrWhiteSpace(x.ChangedBy), () =>
        {
            RuleFor(x => x.ChangedBy)
                .MaximumLength(100)
                .WithMessage("ChangedBy cannot exceed 100 characters")
                .WithErrorCode("CHANGED_BY_TOO_LONG");
        });

        // Date range validation
        When(x => x.StartDate.HasValue && x.EndDate.HasValue, () =>
        {
            RuleFor(x => x.StartDate)
                .LessThanOrEqualTo(x => x.EndDate)
                .WithMessage("StartDate must be less than or equal to EndDate")
                .WithErrorCode("INVALID_DATE_RANGE");
        });

        When(x => x.StartDate.HasValue, () =>
        {
            RuleFor(x => x.StartDate)
                .LessThanOrEqualTo(DateTime.UtcNow)
                .WithMessage("StartDate cannot be in the future")
                .WithErrorCode("FUTURE_START_DATE")
                .GreaterThan(DateTime.UtcNow.AddYears(-10))
                .WithMessage("StartDate cannot be more than 10 years in the past")
                .WithErrorCode("START_DATE_TOO_OLD");
        });

        When(x => x.EndDate.HasValue, () =>
        {
            RuleFor(x => x.EndDate)
                .LessThanOrEqualTo(DateTime.UtcNow)
                .WithMessage("EndDate cannot be in the future")
                .WithErrorCode("FUTURE_END_DATE");
        });

        // Pagination validation
        RuleFor(x => x.Page)
            .GreaterThan(0)
            .WithMessage("Page must be greater than 0")
            .WithErrorCode("INVALID_PAGE")
            .LessThanOrEqualTo(10000)
            .WithMessage("Page cannot exceed 10000")
            .WithErrorCode("PAGE_TOO_HIGH");

        RuleFor(x => x.PageSize)
            .GreaterThan(0)
            .WithMessage("PageSize must be greater than 0")
            .WithErrorCode("INVALID_PAGE_SIZE")
            .LessThanOrEqualTo(10000)
            .WithMessage("PageSize cannot exceed 10000")
            .WithErrorCode("PAGE_SIZE_TOO_LARGE");

        // MaxResults validation
        When(x => x.MaxResults.HasValue, () =>
        {
            RuleFor(x => x.MaxResults)
                .GreaterThan(0)
                .WithMessage("MaxResults must be greater than 0")
                .WithErrorCode("INVALID_MAX_RESULTS")
                .LessThanOrEqualTo(100000)
                .WithMessage("MaxResults cannot exceed 100000")
                .WithErrorCode("MAX_RESULTS_TOO_LARGE");
        });

        // Tiers validation
        When(x => x.Tiers != null && x.Tiers.Any(), () =>
        {
            RuleFor(x => x.Tiers)
                .Must(tiers => tiers.All(t => Enum.IsDefined(typeof(StorageTier), t)))
                .WithMessage("All specified tiers must be valid")
                .WithErrorCode("INVALID_STORAGE_TIER");
        });

        // Business rules
        RuleFor(x => x)
            .Must(HaveReasonableScope)
            .WithMessage("Query scope is too broad. Please specify more restrictive filters.")
            .WithErrorCode("QUERY_TOO_BROAD");

        RuleFor(x => x)
            .Must(HaveValidDateRangeForPerformance)
            .WithMessage("Date range is too large for optimal performance. Consider using smaller date ranges.")
            .WithErrorCode("DATE_RANGE_TOO_LARGE");
    }

    /// <summary>
    /// Ensures the query has reasonable scope to prevent performance issues
    /// </summary>
    private bool HaveReasonableScope(HistoryQuery query)
    {
        // If no filters are specified and requesting large amounts of data, reject
        if (!query.LeadId.HasValue && 
            string.IsNullOrWhiteSpace(query.FieldName) && 
            (query.FieldNames == null || !query.FieldNames.Any()) &&
            string.IsNullOrWhiteSpace(query.ChangedBy) &&
            !query.StartDate.HasValue &&
            query.PageSize > 1000)
        {
            return false;
        }

        return true;
    }

    /// <summary>
    /// Validates that date ranges are reasonable for performance
    /// </summary>
    private bool HaveValidDateRangeForPerformance(HistoryQuery query)
    {
        if (!query.StartDate.HasValue || !query.EndDate.HasValue)
            return true;

        var dateRange = query.EndDate.Value - query.StartDate.Value;
        
        // If querying more than 2 years without specific lead or field filters, warn
        if (dateRange.TotalDays > 730 && 
            !query.LeadId.HasValue && 
            string.IsNullOrWhiteSpace(query.FieldName) &&
            (query.FieldNames == null || !query.FieldNames.Any()))
        {
            return false;
        }

        return true;
    }
}
