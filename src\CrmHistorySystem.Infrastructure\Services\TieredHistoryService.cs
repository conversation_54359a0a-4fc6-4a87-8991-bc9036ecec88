using CrmHistorySystem.Core.Configuration;
using CrmHistorySystem.Core.Interfaces;
using CrmHistorySystem.Core.Models;
using CrmHistorySystem.Infrastructure.Data;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;

namespace CrmHistorySystem.Infrastructure.Services;

/// <summary>
/// Implementation of IHistoryService that provides tiered storage with automatic routing,
/// caching, and performance optimization for CRM history data.
/// </summary>
public class TieredHistoryService : IHistoryService
{
    private readonly ILogger<TieredHistoryService> _logger;
    private readonly HistoryOptions _options;
    private readonly IHistoryCache _cache;
    private readonly IHotTierRepository _hotTierRepository;
    private readonly IWarmTierRepository _warmTierRepository;
    private readonly IColdTierRepository _coldTierRepository;

    public TieredHistoryService(
        ILogger<TieredHistoryService> logger,
        IOptions<HistoryOptions> options,
        IHistoryCache cache,
        IHotTierRepository hotTierRepository,
        IWarmTierRepository warmTierRepository,
        IColdTierRepository coldTierRepository)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _hotTierRepository = hotTierRepository ?? throw new ArgumentNullException(nameof(hotTierRepository));
        _warmTierRepository = warmTierRepository ?? throw new ArgumentNullException(nameof(warmTierRepository));
        _coldTierRepository = coldTierRepository ?? throw new ArgumentNullException(nameof(coldTierRepository));
    }

    /// <summary>
    /// Retrieves history entries with automatic tier routing and caching
    /// </summary>
    public async Task<HistoryResult<HistoryEntry>> GetHistoryAsync(
        HistoryQuery query, 
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var performance = new QueryPerformanceMetrics();
        
        try
        {
            _logger.LogInformation("Executing history query for LeadId: {LeadId}, FieldName: {FieldName}", 
                query.LeadId, query.FieldName);

            // Validate query
            if (!query.IsValid())
            {
                _logger.LogWarning("Invalid query parameters provided");
                return HistoryResult<HistoryEntry>.Empty();
            }

            // Try cache first
            var cacheKey = _cache.GenerateCacheKey(query);
            var cacheStopwatch = Stopwatch.StartNew();
            
            var cachedResult = await _cache.GetAsync<HistoryResult<HistoryEntry>>(cacheKey, cancellationToken);
            performance.CacheTime = cacheStopwatch.Elapsed;
            
            if (cachedResult != null)
            {
                _logger.LogDebug("Cache hit for query: {CacheKey}", cacheKey);
                performance.CacheHits = 1;
                cachedResult.Performance = performance;
                cachedResult.IsFromCache = true;
                return cachedResult;
            }

            performance.CacheMisses = 1;

            // Determine which tiers to query
            var requiredTiers = query.GetRequiredTiers(_options.HotTierRetentionDays, _options.WarmTierRetentionDays);
            var allEntries = new List<HistoryEntry>();
            var totalCount = 0L;

            var dbStopwatch = Stopwatch.StartNew();

            // Query each required tier
            foreach (var tier in requiredTiers.OrderBy(t => t))
            {
                var tierStopwatch = Stopwatch.StartNew();
                var tierQuery = query.ForTier(tier, _options.HotTierRetentionDays, _options.WarmTierRetentionDays);
                
                var (entries, count) = await QueryTierAsync(tier, tierQuery, cancellationToken);
                
                allEntries.AddRange(entries);
                totalCount += count;
                
                performance.TierExecutionTimes[tier] = tierStopwatch.Elapsed;
                performance.DatabaseQueries++;
                
                _logger.LogDebug("Tier {Tier} returned {Count} entries in {Duration}ms", 
                    tier, entries.Count(), tierStopwatch.ElapsedMilliseconds);
            }

            performance.DatabaseTime = dbStopwatch.Elapsed;

            // Sort and paginate results
            var sortedEntries = SortEntries(allEntries, query.SortBy, query.SortOrder);
            var pagedEntries = PaginateEntries(sortedEntries, query.Page, query.PageSize);

            var result = HistoryResult<HistoryEntry>.Success(
                pagedEntries, 
                totalCount, 
                query.Page, 
                query.PageSize);

            result.QueriedTiers = requiredTiers;
            result.Performance = performance;

            // Evaluate SLA compliance
            var slaThreshold = GetSlaThreshold(requiredTiers);
            result.Performance.SlaThresholdMs = slaThreshold;
            result.Performance.MeetsSla = stopwatch.ElapsedMilliseconds <= slaThreshold;

            if (!result.Performance.MeetsSla)
            {
                _logger.LogWarning("Query exceeded SLA threshold. Duration: {Duration}ms, Threshold: {Threshold}ms", 
                    stopwatch.ElapsedMilliseconds, slaThreshold);
                result.Performance.Notes.Add($"Query exceeded SLA threshold by {stopwatch.ElapsedMilliseconds - slaThreshold}ms");
            }

            // Cache the result
            var cacheExpiration = TimeSpan.FromMinutes(_options.CacheExpirationMinutes);
            await _cache.SetAsync(cacheKey, result, cacheExpiration, cancellationToken);

            result.Performance.ExecutionTime = stopwatch.Elapsed;
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing history query");
            throw;
        }
    }

    /// <summary>
    /// Adds a single history entry to the appropriate tier
    /// </summary>
    public async Task<bool> AddHistoryEntryAsync(
        HistoryEntry entry, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Adding history entry for LeadId: {LeadId}, Field: {FieldName}", 
                entry.LeadId, entry.FieldName);

            // Generate checksum and determine tier
            entry.Checksum = entry.GenerateChecksum();
            entry.Tier = entry.DetermineStorageTier(_options.HotTierRetentionDays, _options.WarmTierRetentionDays);

            // Add to appropriate tier
            var success = await AddToTierAsync(entry.Tier, entry, cancellationToken);

            if (success)
            {
                // Invalidate related cache entries
                await InvalidateCacheForEntry(entry, cancellationToken);
                _logger.LogDebug("Successfully added history entry with ID: {Id}", entry.Id);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding history entry for LeadId: {LeadId}", entry.LeadId);
            return false;
        }
    }

    /// <summary>
    /// Adds multiple history entries in batch
    /// </summary>
    public async Task<bool> AddHistoryBatchAsync(
        IEnumerable<HistoryEntry> entries, 
        CancellationToken cancellationToken = default)
    {
        var batch = new HistoryBatch
        {
            Entries = entries.ToList(),
            CreatedBy = "System"
        };

        var result = await ProcessHistoryBatchAsync(batch, cancellationToken);
        return result.Status == BatchStatus.Completed;
    }

    /// <summary>
    /// Processes a history batch with detailed validation and error reporting
    /// </summary>
    public async Task<HistoryBatch> ProcessHistoryBatchAsync(
        HistoryBatch batch, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Processing batch {BatchId} with {Count} entries", 
                batch.BatchId, batch.TotalCount);

            batch.StartProcessing();

            // Validate all entries
            if (!batch.ValidateEntries())
            {
                batch.MarkAsFailed($"Validation failed with {batch.ValidationErrors.Count} errors");
                return batch;
            }

            // Generate checksums and determine tiers
            batch.GenerateChecksums();
            batch.DetermineStorageTiers(_options.HotTierRetentionDays, _options.WarmTierRetentionDays);

            // Group by tier for efficient processing
            var tierGroups = batch.GroupByTier();
            var processedCount = 0;
            var failedCount = 0;

            foreach (var tierGroup in tierGroups)
            {
                try
                {
                    var tierProcessed = await ProcessTierBatchAsync(tierGroup.Key, tierGroup.Value, cancellationToken);
                    processedCount += tierProcessed;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing batch for tier {Tier}", tierGroup.Key);
                    failedCount += tierGroup.Value.Count;
                    
                    batch.ProcessingErrors.Add(new BatchProcessingError
                    {
                        ErrorMessage = ex.Message,
                        ErrorCode = "TIER_PROCESSING_FAILED",
                        StackTrace = ex.StackTrace
                    });
                }
            }

            batch.CompleteProcessing(processedCount, failedCount);

            // Invalidate cache for affected entries
            await InvalidateCacheForBatch(batch, cancellationToken);

            _logger.LogInformation("Batch {BatchId} completed. Processed: {Processed}, Failed: {Failed}", 
                batch.BatchId, processedCount, failedCount);

            return batch;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing batch {BatchId}", batch.BatchId);
            batch.MarkAsFailed(ex.Message);
            return batch;
        }
    }

    /// <summary>
    /// Archives old entries by moving them to cold storage
    /// </summary>
    public async Task<int> ArchiveOldEntriesAsync(
        DateTime cutoffDate, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting archival process for entries older than {CutoffDate}", cutoffDate);

            var archivedCount = 0;

            // Move from hot to warm tier
            var hotToWarm = await _hotTierRepository.ArchiveToWarmTierAsync(
                cutoffDate.AddDays(-_options.HotTierRetentionDays), 
                _options.Archival.ArchivalBatchSize, 
                cancellationToken);
            
            archivedCount += hotToWarm;

            // Move from warm to cold tier
            var warmToCold = await _warmTierRepository.ArchiveToColdTierAsync(
                cutoffDate.AddDays(-_options.WarmTierRetentionDays), 
                _options.Archival.ArchivalBatchSize, 
                cancellationToken);
            
            archivedCount += warmToCold;

            _logger.LogInformation("Archival completed. Total archived: {Count}", archivedCount);
            return archivedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during archival process");
            throw;
        }
    }

    /// <summary>
    /// Gets history for a specific lead with optimized caching
    /// </summary>
    public async Task<HistoryResult<HistoryEntry>> GetLeadHistoryAsync(
        int leadId,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageSize = 100,
        int page = 1,
        CancellationToken cancellationToken = default)
    {
        var query = new HistoryQuery
        {
            LeadId = leadId,
            StartDate = startDate,
            EndDate = endDate,
            PageSize = pageSize,
            Page = page
        };

        return await GetHistoryAsync(query, cancellationToken);
    }

    /// <summary>
    /// Gets history for a specific field across all leads
    /// </summary>
    public async Task<HistoryResult<HistoryEntry>> GetFieldHistoryAsync(
        string fieldName,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int pageSize = 100,
        int page = 1,
        CancellationToken cancellationToken = default)
    {
        var query = new HistoryQuery
        {
            FieldName = fieldName,
            StartDate = startDate,
            EndDate = endDate,
            PageSize = pageSize,
            Page = page
        };

        return await GetHistoryAsync(query, cancellationToken);
    }

    // Additional methods will be implemented in the next part due to length constraints
    public Task<HistoryStatistics> GetHistoryStatisticsAsync(DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<DataIntegrityResult> ValidateDataIntegrityAsync(int sampleSize = 1000, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<TierMigrationResult> MigrateBetweenTiersAsync(bool dryRun = false, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    // Private helper methods
    private async Task<(IEnumerable<HistoryEntry> entries, long totalCount)> QueryTierAsync(
        StorageTier tier, 
        HistoryQuery query, 
        CancellationToken cancellationToken)
    {
        return tier switch
        {
            StorageTier.Hot => await _hotTierRepository.QueryAsync(query, cancellationToken),
            StorageTier.Warm => await _warmTierRepository.QueryAsync(query, cancellationToken),
            StorageTier.Cold => await _coldTierRepository.QueryAsync(query, cancellationToken),
            _ => throw new ArgumentException($"Unknown storage tier: {tier}")
        };
    }

    private async Task<bool> AddToTierAsync(StorageTier tier, HistoryEntry entry, CancellationToken cancellationToken)
    {
        return tier switch
        {
            StorageTier.Hot => await _hotTierRepository.AddAsync(entry, cancellationToken),
            StorageTier.Warm => await _warmTierRepository.AddAsync(entry, cancellationToken),
            StorageTier.Cold => await _coldTierRepository.AddAsync(entry, cancellationToken),
            _ => throw new ArgumentException($"Unknown storage tier: {tier}")
        };
    }

    private async Task<int> ProcessTierBatchAsync(StorageTier tier, IList<HistoryEntry> entries, CancellationToken cancellationToken)
    {
        return tier switch
        {
            StorageTier.Hot => await _hotTierRepository.AddBatchAsync(entries, cancellationToken),
            StorageTier.Warm => await _warmTierRepository.AddBatchAsync(entries, cancellationToken),
            StorageTier.Cold => await _coldTierRepository.AddBatchAsync(entries, cancellationToken),
            _ => throw new ArgumentException($"Unknown storage tier: {tier}")
        };
    }

    private IEnumerable<HistoryEntry> SortEntries(IEnumerable<HistoryEntry> entries, SortField sortBy, SortOrder sortOrder)
    {
        var query = sortBy switch
        {
            SortField.ChangedAt => sortOrder == SortOrder.Ascending 
                ? entries.OrderBy(e => e.ChangedAt) 
                : entries.OrderByDescending(e => e.ChangedAt),
            SortField.LeadId => sortOrder == SortOrder.Ascending 
                ? entries.OrderBy(e => e.LeadId) 
                : entries.OrderByDescending(e => e.LeadId),
            SortField.FieldName => sortOrder == SortOrder.Ascending 
                ? entries.OrderBy(e => e.FieldName) 
                : entries.OrderByDescending(e => e.FieldName),
            SortField.ChangedBy => sortOrder == SortOrder.Ascending 
                ? entries.OrderBy(e => e.ChangedBy) 
                : entries.OrderByDescending(e => e.ChangedBy),
            _ => entries.OrderByDescending(e => e.ChangedAt)
        };

        return query;
    }

    private IEnumerable<HistoryEntry> PaginateEntries(IEnumerable<HistoryEntry> entries, int page, int pageSize)
    {
        return entries.Skip((page - 1) * pageSize).Take(pageSize);
    }

    private int GetSlaThreshold(IEnumerable<StorageTier> tiers)
    {
        if (tiers.Contains(StorageTier.Cold))
            return _options.Performance.ColdTierMaxResponseTimeMs;
        if (tiers.Contains(StorageTier.Warm))
            return _options.Performance.WarmTierMaxResponseTimeMs;
        return _options.Performance.HotTierMaxResponseTimeMs;
    }

    private async Task InvalidateCacheForEntry(HistoryEntry entry, CancellationToken cancellationToken)
    {
        await _cache.InvalidateLeadCacheAsync(entry.LeadId, cancellationToken);
        await _cache.InvalidateFieldCacheAsync(entry.FieldName, cancellationToken);
    }

    private async Task InvalidateCacheForBatch(HistoryBatch batch, CancellationToken cancellationToken)
    {
        var leadIds = batch.Entries.Select(e => e.LeadId).Distinct();
        var fieldNames = batch.Entries.Select(e => e.FieldName).Distinct();

        foreach (var leadId in leadIds)
        {
            await _cache.InvalidateLeadCacheAsync(leadId, cancellationToken);
        }

        foreach (var fieldName in fieldNames)
        {
            await _cache.InvalidateFieldCacheAsync(fieldName, cancellationToken);
        }
    }
}
