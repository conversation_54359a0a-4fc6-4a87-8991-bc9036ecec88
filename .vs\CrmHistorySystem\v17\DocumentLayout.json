{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{9C06522F-88AA-4CA1-AB7C-75E2FE2E50FA}|src\\CrmHistorySystem.Infrastructure\\CrmHistorySystem.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\lrb project\\history new implementation\\src\\crmhistorysystem.infrastructure\\data\\hottierrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9C06522F-88AA-4CA1-AB7C-75E2FE2E50FA}|src\\CrmHistorySystem.Infrastructure\\CrmHistorySystem.Infrastructure.csproj|solutionrelative:src\\crmhistorysystem.infrastructure\\data\\hottierrepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9C06522F-88AA-4CA1-AB7C-75E2FE2E50FA}|src\\CrmHistorySystem.Infrastructure\\CrmHistorySystem.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\lrb project\\history new implementation\\src\\crmhistorysystem.infrastructure\\caching\\redishistorycache.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9C06522F-88AA-4CA1-AB7C-75E2FE2E50FA}|src\\CrmHistorySystem.Infrastructure\\CrmHistorySystem.Infrastructure.csproj|solutionrelative:src\\crmhistorysystem.infrastructure\\caching\\redishistorycache.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9C06522F-88AA-4CA1-AB7C-75E2FE2E50FA}|src\\CrmHistorySystem.Infrastructure\\CrmHistorySystem.Infrastructure.csproj|c:\\users\\<USER>\\desktop\\lrb project\\history new implementation\\src\\crmhistorysystem.infrastructure\\services\\tieredhistoryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9C06522F-88AA-4CA1-AB7C-75E2FE2E50FA}|src\\CrmHistorySystem.Infrastructure\\CrmHistorySystem.Infrastructure.csproj|solutionrelative:src\\crmhistorysystem.infrastructure\\services\\tieredhistoryservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{AE4021C9-7A5D-483B-B954-E7033BC45E1B}|tests\\CrmHistorySystem.Tests\\CrmHistorySystem.Tests.csproj|c:\\users\\<USER>\\desktop\\lrb project\\history new implementation\\tests\\crmhistorysystem.tests\\unit\\models\\historyquerytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{AE4021C9-7A5D-483B-B954-E7033BC45E1B}|tests\\CrmHistorySystem.Tests\\CrmHistorySystem.Tests.csproj|solutionrelative:tests\\crmhistorysystem.tests\\unit\\models\\historyquerytests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\CrmHistorySystem.Core\\CrmHistorySystem.Core.csproj|c:\\users\\<USER>\\desktop\\lrb project\\history new implementation\\src\\crmhistorysystem.core\\interfaces\\ihistorycache.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\CrmHistorySystem.Core\\CrmHistorySystem.Core.csproj|solutionrelative:src\\crmhistorysystem.core\\interfaces\\ihistorycache.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\CrmHistorySystem.Core\\CrmHistorySystem.Core.csproj|c:\\users\\<USER>\\desktop\\lrb project\\history new implementation\\src\\crmhistorysystem.core\\models\\historyresult.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\CrmHistorySystem.Core\\CrmHistorySystem.Core.csproj|solutionrelative:src\\crmhistorysystem.core\\models\\historyresult.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7573D2FD-0C1E-4BA2-8D47-A3156E5F27CD}|src\\CrmHistorySystem.Api\\CrmHistorySystem.Api.csproj|c:\\users\\<USER>\\desktop\\lrb project\\history new implementation\\src\\crmhistorysystem.api\\controllers\\historycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7573D2FD-0C1E-4BA2-8D47-A3156E5F27CD}|src\\CrmHistorySystem.Api\\CrmHistorySystem.Api.csproj|solutionrelative:src\\crmhistorysystem.api\\controllers\\historycontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:128:0:{75188d03-9892-4ae2-abf1-207126247ce5}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "HotTierRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\Data\\HotTierRepository.cs", "RelativeDocumentMoniker": "src\\CrmHistorySystem.Infrastructure\\Data\\HotTierRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\Data\\HotTierRepository.cs", "RelativeToolTip": "src\\CrmHistorySystem.Infrastructure\\Data\\HotTierRepository.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAANAAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:42:12.519Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "RedisHistoryCache.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\Caching\\RedisHistoryCache.cs", "RelativeDocumentMoniker": "src\\CrmHistorySystem.Infrastructure\\Caching\\RedisHistoryCache.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\Caching\\RedisHistoryCache.cs", "RelativeToolTip": "src\\CrmHistorySystem.Infrastructure\\Caching\\RedisHistoryCache.cs", "ViewState": "AgIAAGMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:19:59.923Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "TieredHistoryService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\Services\\TieredHistoryService.cs", "RelativeDocumentMoniker": "src\\CrmHistorySystem.Infrastructure\\Services\\TieredHistoryService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Infrastructure\\Services\\TieredHistoryService.cs", "RelativeToolTip": "src\\CrmHistorySystem.Infrastructure\\Services\\TieredHistoryService.cs", "ViewState": "AgIAAIoAAAAAAAAAAAAAAJ4AAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:14:28.308Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "HistoryQueryTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\tests\\CrmHistorySystem.Tests\\Unit\\Models\\HistoryQueryTests.cs", "RelativeDocumentMoniker": "tests\\CrmHistorySystem.Tests\\Unit\\Models\\HistoryQueryTests.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\tests\\CrmHistorySystem.Tests\\Unit\\Models\\HistoryQueryTests.cs", "RelativeToolTip": "tests\\CrmHistorySystem.Tests\\Unit\\Models\\HistoryQueryTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:13:57.721Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "IHistoryCache.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\Interfaces\\IHistoryCache.cs", "RelativeDocumentMoniker": "src\\CrmHistorySystem.Core\\Interfaces\\IHistoryCache.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\Interfaces\\IHistoryCache.cs", "RelativeToolTip": "src\\CrmHistorySystem.Core\\Interfaces\\IHistoryCache.cs", "ViewState": "AgIAALQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:13:17.067Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "HistoryResult.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\Models\\HistoryResult.cs", "RelativeDocumentMoniker": "src\\CrmHistorySystem.Core\\Models\\HistoryResult.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Core\\Models\\HistoryResult.cs", "RelativeToolTip": "src\\CrmHistorySystem.Core\\Models\\HistoryResult.cs", "ViewState": "AgIAAF4AAAAAAAAAAAAYwHEAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:11:16.313Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "HistoryController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Api\\Controllers\\HistoryController.cs", "RelativeDocumentMoniker": "src\\CrmHistorySystem.Api\\Controllers\\HistoryController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\Lrb Project\\History New Implementation\\src\\CrmHistorySystem.Api\\Controllers\\HistoryController.cs", "RelativeToolTip": "src\\CrmHistorySystem.Api\\Controllers\\HistoryController.cs", "ViewState": "AgIAAEEBAAAAAAAAAAAAAFEBAABUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:10:38.234Z"}]}]}]}