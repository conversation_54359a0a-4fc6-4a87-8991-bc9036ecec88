using CrmHistorySystem.Core.Models;
using FluentAssertions;
using Xunit;

namespace CrmHistorySystem.Tests.Unit.Models;

/// <summary>
/// Unit tests for HistoryQuery model
/// </summary>
public class HistoryQueryTests
{
    [Fact]
    public void GetRequiredTiers_NoDateRange_ShouldReturnAllTiers()
    {
        // Arrange
        var query = new HistoryQuery();

        // Act
        var tiers = query.GetRequiredTiers();

        // Assert
        tiers.Should().HaveCount(3);
        tiers.Should().Contain(StorageTier.Hot);
        tiers.Should().Contain(StorageTier.Warm);
        tiers.Should().Contain(StorageTier.Cold);
    }

    [Fact]
    public void GetRequiredTiers_RecentDateRange_ShouldReturnHotTierOnly()
    {
        // Arrange
        var query = new HistoryQuery
        {
            StartDate = DateTime.UtcNow.AddDays(-30),
            EndDate = DateTime.UtcNow
        };

        // Act
        var tiers = query.GetRequiredTiers(hotTierDays: 90, warmTierDays: 365);

        // Assert
        tiers.Should().HaveCount(1);
        tiers.Should().Contain(StorageTier.Hot);
    }

    [Fact]
    public void GetRequiredTiers_MediumDateRange_ShouldReturnHotAndWarmTiers()
    {
        // Arrange
        var query = new HistoryQuery
        {
            StartDate = DateTime.UtcNow.AddDays(-180),
            EndDate = DateTime.UtcNow
        };

        // Act
        var tiers = query.GetRequiredTiers(hotTierDays: 90, warmTierDays: 365);

        // Assert
        tiers.Should().HaveCount(2);
        tiers.Should().Contain(StorageTier.Hot);
        tiers.Should().Contain(StorageTier.Warm);
    }

    [Fact]
    public void GetRequiredTiers_OldDateRange_ShouldReturnAllTiers()
    {
        // Arrange
        var query = new HistoryQuery
        {
            StartDate = DateTime.UtcNow.AddDays(-400),
            EndDate = DateTime.UtcNow
        };

        // Act
        var tiers = query.GetRequiredTiers(hotTierDays: 90, warmTierDays: 365);

        // Assert
        tiers.Should().HaveCount(3);
        tiers.Should().Contain(StorageTier.Hot);
        tiers.Should().Contain(StorageTier.Warm);
        tiers.Should().Contain(StorageTier.Cold);
    }

    [Fact]
    public void GetRequiredTiers_VeryOldDateRange_ShouldReturnColdTierOnly()
    {
        // Arrange
        var query = new HistoryQuery
        {
            StartDate = DateTime.UtcNow.AddDays(-500),
            EndDate = DateTime.UtcNow.AddDays(-400)
        };

        // Act
        var tiers = query.GetRequiredTiers(hotTierDays: 90, warmTierDays: 365);

        // Assert
        tiers.Should().HaveCount(1);
        tiers.Should().Contain(StorageTier.Cold);
    }

    [Fact]
    public void GetSkipCount_CalculatesCorrectly()
    {
        // Arrange
        var query = new HistoryQuery
        {
            Page = 3,
            PageSize = 50
        };

        // Act
        var skipCount = query.GetSkipCount();

        // Assert
        skipCount.Should().Be(100); // (3-1) * 50
    }

    [Fact]
    public void IsValid_ValidQuery_ReturnsTrue()
    {
        // Arrange
        var query = new HistoryQuery
        {
            LeadId = 12345,
            StartDate = DateTime.UtcNow.AddDays(-30),
            EndDate = DateTime.UtcNow,
            Page = 1,
            PageSize = 100
        };

        // Act
        var isValid = query.IsValid();

        // Assert
        isValid.Should().BeTrue();
    }

    [Fact]
    public void IsValid_InvalidDateRange_ReturnsFalse()
    {
        // Arrange
        var query = new HistoryQuery
        {
            StartDate = DateTime.UtcNow,
            EndDate = DateTime.UtcNow.AddDays(-30) // End date before start date
        };

        // Act
        var isValid = query.IsValid();

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void IsValid_InvalidPagination_ReturnsFalse()
    {
        // Arrange
        var query = new HistoryQuery
        {
            Page = 0, // Invalid page number
            PageSize = 100
        };

        // Act
        var isValid = query.IsValid();

        // Assert
        isValid.Should().BeFalse();
    }

    [Fact]
    public void ForTier_HotTier_AdjustsDateRangeCorrectly()
    {
        // Arrange
        var now = DateTime.UtcNow;
        var query = new HistoryQuery
        {
            StartDate = now.AddDays(-400),
            EndDate = now
        };

        // Act
        var hotQuery = query.ForTier(StorageTier.Hot, hotTierDays: 90, warmTierDays: 365);

        // Assert
        hotQuery.StartDate.Should().BeCloseTo(now.AddDays(-90), TimeSpan.FromSeconds(1));
        hotQuery.EndDate.Should().Be(now);
    }

    [Fact]
    public void ForTier_WarmTier_AdjustsDateRangeCorrectly()
    {
        // Arrange
        var now = DateTime.UtcNow;
        var query = new HistoryQuery
        {
            StartDate = now.AddDays(-400),
            EndDate = now
        };

        // Act
        var warmQuery = query.ForTier(StorageTier.Warm, hotTierDays: 90, warmTierDays: 365);

        // Assert
        warmQuery.StartDate.Should().BeCloseTo(now.AddDays(-365), TimeSpan.FromSeconds(1));
        warmQuery.EndDate.Should().BeCloseTo(now.AddDays(-90), TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void ForTier_ColdTier_AdjustsDateRangeCorrectly()
    {
        // Arrange
        var now = DateTime.UtcNow;
        var query = new HistoryQuery
        {
            StartDate = DateTime.MinValue,
            EndDate = now
        };

        // Act
        var coldQuery = query.ForTier(StorageTier.Cold, hotTierDays: 90, warmTierDays: 365);

        // Assert
        coldQuery.StartDate.Should().Be(DateTime.MinValue);
        coldQuery.EndDate.Should().BeCloseTo(now.AddDays(-365), TimeSpan.FromSeconds(1));
    }

    [Fact]
    public void ForTier_PreservesOtherProperties()
    {
        // Arrange
        var query = new HistoryQuery
        {
            LeadId = 12345,
            FieldName = "Email",
            ChangedBy = "<EMAIL>",
            Page = 2,
            PageSize = 50,
            SortOrder = SortOrder.Ascending,
            SortBy = SortField.FieldName,
            IncludeMetadata = true,
            MaxResults = 1000
        };

        // Act
        var tierQuery = query.ForTier(StorageTier.Hot);

        // Assert
        tierQuery.LeadId.Should().Be(query.LeadId);
        tierQuery.FieldName.Should().Be(query.FieldName);
        tierQuery.ChangedBy.Should().Be(query.ChangedBy);
        tierQuery.Page.Should().Be(query.Page);
        tierQuery.PageSize.Should().Be(query.PageSize);
        tierQuery.SortOrder.Should().Be(query.SortOrder);
        tierQuery.SortBy.Should().Be(query.SortBy);
        tierQuery.IncludeMetadata.Should().Be(query.IncludeMetadata);
        tierQuery.MaxResults.Should().Be(query.MaxResults);
    }
}
