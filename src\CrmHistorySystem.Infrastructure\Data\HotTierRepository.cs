using CrmHistorySystem.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace CrmHistorySystem.Infrastructure.Data;

/// <summary>
/// Repository implementation for hot tier storage (SQL Server optimized for speed)
/// Handles recent data (0-3 months) with maximum performance characteristics
/// </summary>
public class HotTierRepository : BaseEfRepository<HistoryDbContext>, IHotTierRepository
{
    private readonly ILogger<HotTierRepository> _logger;

    public HotTierRepository(HistoryDbContext context, ILogger<HotTierRepository> logger) 
        : base(context, logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// Queries history entries from hot tier storage
    /// </summary>
    public async Task<(IEnumerable<HistoryEntry> entries, long totalCount)> QueryAsync(
        HistoryQuery query, 
        CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Executing hot tier query for LeadId: {LeadId}, FieldName: {FieldName}", 
            query.LeadId, query.FieldName);

        return await ExecuteQueryAsync(query, cancellationToken);
    }

    /// <summary>
    /// Adds a single history entry to hot tier
    /// </summary>
    public async Task<bool> AddAsync(HistoryEntry entry, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Adding entry to hot tier for LeadId: {LeadId}, Field: {FieldName}", 
                entry.LeadId, entry.FieldName);

            entry.Tier = StorageTier.Hot;
            Context.HistoryEntries.Add(entry);
            
            var result = await Context.SaveChangesAsync(cancellationToken);
            return result > 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding entry to hot tier for LeadId: {LeadId}", entry.LeadId);
            return false;
        }
    }

    /// <summary>
    /// Adds multiple history entries in batch to hot tier
    /// </summary>
    public async Task<int> AddBatchAsync(IEnumerable<HistoryEntry> entries, CancellationToken cancellationToken = default)
    {
        var entriesList = entries.ToList();
        
        _logger.LogDebug("Adding batch of {Count} entries to hot tier", entriesList.Count);

        // Ensure all entries are marked as hot tier
        foreach (var entry in entriesList)
        {
            entry.Tier = StorageTier.Hot;
        }

        return await AddBatchInternalAsync(entriesList, cancellationToken);
    }

    /// <summary>
    /// Gets the total count of entries in hot tier
    /// </summary>
    public async Task<long> GetTotalCountAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            return await Context.HistoryEntries.LongCountAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total count from hot tier");
            return 0;
        }
    }

    /// <summary>
    /// Gets storage statistics for hot tier
    /// </summary>
    public async Task<TierStorageStatistics> GetStorageStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var stats = new TierStorageStatistics
            {
                Tier = StorageTier.Hot,
                LastUpdated = DateTime.UtcNow
            };

            // Get basic counts and dates
            var entries = Context.HistoryEntries.AsQueryable();
            
            stats.TotalEntries = await entries.LongCountAsync(cancellationToken);
            
            if (stats.TotalEntries > 0)
            {
                stats.OldestEntry = await entries.MinAsync(e => e.ChangedAt, cancellationToken);
                stats.NewestEntry = await entries.MaxAsync(e => e.ChangedAt, cancellationToken);

                // Get entries by field
                stats.EntriesByField = await entries
                    .GroupBy(e => e.FieldName)
                    .Select(g => new { Field = g.Key, Count = g.LongCount() })
                    .ToDictionaryAsync(x => x.Field, x => x.Count, cancellationToken);

                // Get entries by user
                stats.EntriesByUser = await entries
                    .GroupBy(e => e.ChangedBy)
                    .Select(g => new { User = g.Key, Count = g.LongCount() })
                    .ToDictionaryAsync(x => x.User, x => x.Count, cancellationToken);

                // Estimate storage size (rough calculation)
                var avgFieldNameLength = await entries.AverageAsync(e => e.FieldName.Length, cancellationToken);
                var avgOldValueLength = await entries.Where(e => e.OldValue != null)
                    .AverageAsync(e => e.OldValue!.Length, cancellationToken);
                var avgNewValueLength = await entries.Where(e => e.NewValue != null)
                    .AverageAsync(e => e.NewValue!.Length, cancellationToken);
                var avgChangedByLength = await entries.AverageAsync(e => e.ChangedBy.Length, cancellationToken);

                stats.AverageEntrySizeBytes = 
                    8 + // Id (bigint)
                    4 + // LeadId (int)
                    avgFieldNameLength * 2 + // FieldName (nvarchar)
                    avgOldValueLength * 2 + // OldValue (nvarchar)
                    avgNewValueLength * 2 + // NewValue (nvarchar)
                    8 + // ChangedAt (datetime2)
                    avgChangedByLength * 2 + // ChangedBy (nvarchar)
                    100; // Metadata and other fields

                stats.TotalSizeBytes = (long)(stats.TotalEntries * stats.AverageEntrySizeBytes);
            }

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting storage statistics for hot tier");
            return new TierStorageStatistics { Tier = StorageTier.Hot };
        }
    }

    /// <summary>
    /// Validates data integrity for entries in hot tier
    /// </summary>
    public async Task<DataIntegrityResult> ValidateIntegrityAsync(int sampleSize, CancellationToken cancellationToken = default)
    {
        var result = new DataIntegrityResult();

        try
        {
            _logger.LogInformation("Starting data integrity validation for hot tier with sample size: {SampleSize}", sampleSize);

            var query = Context.HistoryEntries.AsQueryable();
            
            if (sampleSize > 0)
            {
                // Take a random sample for validation
                var totalCount = await query.CountAsync(cancellationToken);
                var skip = totalCount > sampleSize ? new Random().Next(0, totalCount - sampleSize) : 0;
                query = query.Skip(skip).Take(sampleSize);
            }

            var entries = await query.ToListAsync(cancellationToken);
            result.TotalValidated = entries.Count;

            foreach (var entry in entries)
            {
                // Validate checksum if present
                if (!string.IsNullOrEmpty(entry.Checksum))
                {
                    var expectedChecksum = entry.GenerateChecksum();
                    if (entry.Checksum != expectedChecksum)
                    {
                        result.CorruptedEntries++;
                        result.Errors.Add(new DataIntegrityError
                        {
                            EntryId = entry.Id,
                            Tier = StorageTier.Hot,
                            ErrorType = "CHECKSUM_MISMATCH",
                            Description = "Entry checksum does not match calculated value",
                            ExpectedChecksum = expectedChecksum,
                            ActualChecksum = entry.Checksum
                        });
                    }
                }
                else
                {
                    result.MissingChecksums++;
                }
            }

            _logger.LogInformation("Hot tier integrity validation completed. Validated: {Total}, Corrupted: {Corrupted}, Missing Checksums: {Missing}",
                result.TotalValidated, result.CorruptedEntries, result.MissingChecksums);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during hot tier integrity validation");
            result.Errors.Add(new DataIntegrityError
            {
                Tier = StorageTier.Hot,
                ErrorType = "VALIDATION_ERROR",
                Description = ex.Message
            });
            return result;
        }
    }

    /// <summary>
    /// Archives old entries to warm tier
    /// </summary>
    public async Task<int> ArchiveToWarmTierAsync(DateTime cutoffDate, int batchSize, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting archival from hot to warm tier for entries older than {CutoffDate}", cutoffDate);

            var totalArchived = 0;
            var hasMoreData = true;

            while (hasMoreData && !cancellationToken.IsCancellationRequested)
            {
                // Get a batch of entries to archive
                var entriesToArchive = await Context.HistoryEntries
                    .Where(e => e.ChangedAt < cutoffDate)
                    .OrderBy(e => e.ChangedAt)
                    .Take(batchSize)
                    .ToListAsync(cancellationToken);

                if (!entriesToArchive.Any())
                {
                    hasMoreData = false;
                    break;
                }

                using var transaction = await Context.Database.BeginTransactionAsync(cancellationToken);

                try
                {
                    // TODO: In a real implementation, this would involve:
                    // 1. Insert entries into warm tier database
                    // 2. Verify successful insertion
                    // 3. Delete from hot tier
                    // For now, we'll just mark them as warm tier and update
                    
                    foreach (var entry in entriesToArchive)
                    {
                        entry.Tier = StorageTier.Warm;
                    }

                    await Context.SaveChangesAsync(cancellationToken);
                    await transaction.CommitAsync(cancellationToken);

                    totalArchived += entriesToArchive.Count;
                    
                    _logger.LogDebug("Archived batch of {Count} entries to warm tier", entriesToArchive.Count);
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync(cancellationToken);
                    _logger.LogError(ex, "Error archiving batch to warm tier");
                    throw;
                }
            }

            _logger.LogInformation("Hot to warm tier archival completed. Total archived: {Count}", totalArchived);
            return totalArchived;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during hot to warm tier archival");
            throw;
        }
    }

    /// <summary>
    /// Gets recent activity statistics for hot tier
    /// </summary>
    public async Task<ActivityStatistics> GetRecentActivityAsync(int hours = 24, CancellationToken cancellationToken = default)
    {
        try
        {
            var startTime = DateTime.UtcNow.AddHours(-hours);
            var endTime = DateTime.UtcNow;

            var stats = new ActivityStatistics
            {
                PeriodStart = startTime,
                PeriodEnd = endTime
            };

            var recentEntries = await Context.HistoryEntries
                .Where(e => e.ChangedAt >= startTime)
                .ToListAsync(cancellationToken);

            stats.TotalWrites = recentEntries.Count;

            // Group by field
            stats.WritesByField = recentEntries
                .GroupBy(e => e.FieldName)
                .ToDictionary(g => g.Key, g => g.Count());

            // Group by hour
            stats.WritesByHour = recentEntries
                .GroupBy(e => e.ChangedAt.Hour)
                .ToDictionary(g => g.Key, g => g.Count());

            // Calculate average write time (this would need to be tracked separately in a real implementation)
            stats.AverageWriteTimeMs = 5.0; // Placeholder value

            _logger.LogDebug("Retrieved activity statistics for hot tier: {Writes} writes in last {Hours} hours",
                stats.TotalWrites, hours);

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recent activity statistics for hot tier");
            return new ActivityStatistics();
        }
    }
}
