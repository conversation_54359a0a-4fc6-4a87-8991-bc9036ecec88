-- =============================================
-- CRM History System - Hot Tier Database Schema
-- Creates optimized tables for recent data (0-3 months)
-- Performance target: <50ms query response time
-- =============================================

USE [CrmHistory_Hot]
GO

-- Create the main history entries table for hot tier
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[HistoryEntries_Hot]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[HistoryEntries_Hot](
        [Id] [bigint] IDENTITY(1,1) NOT NULL,
        [LeadId] [int] NOT NULL,
        [FieldName] [nvarchar](100) NOT NULL,
        [OldValue] [nvarchar](4000) NULL,
        [NewValue] [nvarchar](4000) NULL,
        [ChangedAt] [datetime2](7) NOT NULL,
        [ChangedBy] [nvarchar](100) NOT NULL,
        [Metadata] [nvarchar](8000) NULL,
        [Tier] [int] NOT NULL DEFAULT(1), -- 1 = Hot
        [Checksum] [nvarchar](100) NULL,
        [CreatedAt] [datetime2](7) NOT NULL DEFAULT(GETUTCDATE()),
        [UpdatedAt] [datetime2](7) NOT NULL DEFAULT(GETUTCDATE()),
        CONSTRAINT [PK_HistoryEntries_Hot] PRIMARY KEY CLUSTERED ([Id] ASC)
    ) ON [PRIMARY]
END
GO

-- Create optimized indexes for hot tier queries
-- Primary index for lead-based queries (most common)
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[HistoryEntries_Hot]') AND name = N'IX_HistoryEntries_Hot_LeadId_ChangedAt')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Hot_LeadId_ChangedAt] 
    ON [dbo].[HistoryEntries_Hot] ([LeadId] ASC, [ChangedAt] DESC)
    INCLUDE ([FieldName], [OldValue], [NewValue], [ChangedBy])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
END
GO

-- Index for field-based queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[HistoryEntries_Hot]') AND name = N'IX_HistoryEntries_Hot_FieldName_ChangedAt')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Hot_FieldName_ChangedAt] 
    ON [dbo].[HistoryEntries_Hot] ([FieldName] ASC, [ChangedAt] DESC)
    INCLUDE ([LeadId], [OldValue], [NewValue], [ChangedBy])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
END
GO

-- Index for date-based queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[HistoryEntries_Hot]') AND name = N'IX_HistoryEntries_Hot_ChangedAt')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Hot_ChangedAt] 
    ON [dbo].[HistoryEntries_Hot] ([ChangedAt] DESC)
    INCLUDE ([LeadId], [FieldName], [ChangedBy])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
END
GO

-- Index for user-based queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[HistoryEntries_Hot]') AND name = N'IX_HistoryEntries_Hot_ChangedBy')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Hot_ChangedBy] 
    ON [dbo].[HistoryEntries_Hot] ([ChangedBy] ASC, [ChangedAt] DESC)
    INCLUDE ([LeadId], [FieldName])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
END
GO

-- Index for tier-based operations
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[HistoryEntries_Hot]') AND name = N'IX_HistoryEntries_Hot_Tier_ChangedAt')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Hot_Tier_ChangedAt] 
    ON [dbo].[HistoryEntries_Hot] ([Tier] ASC, [ChangedAt] ASC)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
END
GO

-- Create batch processing table for tracking bulk operations
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[BatchOperations]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[BatchOperations](
        [BatchId] [uniqueidentifier] NOT NULL,
        [OperationType] [nvarchar](50) NOT NULL,
        [Status] [int] NOT NULL, -- 0=Pending, 1=Processing, 2=Completed, 3=Failed
        [TotalEntries] [int] NOT NULL,
        [ProcessedEntries] [int] NOT NULL DEFAULT(0),
        [FailedEntries] [int] NOT NULL DEFAULT(0),
        [CreatedAt] [datetime2](7) NOT NULL DEFAULT(GETUTCDATE()),
        [StartedAt] [datetime2](7) NULL,
        [CompletedAt] [datetime2](7) NULL,
        [ErrorMessage] [nvarchar](max) NULL,
        [CreatedBy] [nvarchar](100) NOT NULL,
        CONSTRAINT [PK_BatchOperations] PRIMARY KEY CLUSTERED ([BatchId] ASC)
    ) ON [PRIMARY]
END
GO

-- Create performance monitoring table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PerformanceMetrics]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[PerformanceMetrics](
        [Id] [bigint] IDENTITY(1,1) NOT NULL,
        [OperationType] [nvarchar](50) NOT NULL,
        [ExecutionTimeMs] [int] NOT NULL,
        [RecordsProcessed] [int] NOT NULL,
        [MemoryUsageMB] [decimal](10,2) NULL,
        [CacheHit] [bit] NOT NULL DEFAULT(0),
        [QueryParameters] [nvarchar](max) NULL,
        [ExecutedAt] [datetime2](7) NOT NULL DEFAULT(GETUTCDATE()),
        [UserId] [nvarchar](100) NULL,
        CONSTRAINT [PK_PerformanceMetrics] PRIMARY KEY CLUSTERED ([Id] ASC)
    ) ON [PRIMARY]
END
GO

-- Index for performance analysis
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[PerformanceMetrics]') AND name = N'IX_PerformanceMetrics_OperationType_ExecutedAt')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_PerformanceMetrics_OperationType_ExecutedAt] 
    ON [dbo].[PerformanceMetrics] ([OperationType] ASC, [ExecutedAt] DESC)
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
END
GO

-- Create stored procedure for high-performance batch inserts
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_InsertHistoryBatch]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_InsertHistoryBatch]
GO

CREATE PROCEDURE [dbo].[sp_InsertHistoryBatch]
    @BatchData NVARCHAR(MAX), -- JSON array of history entries
    @BatchId UNIQUEIDENTIFIER,
    @CreatedBy NVARCHAR(100)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ProcessedCount INT = 0;
    DECLARE @ErrorMessage NVARCHAR(MAX) = NULL;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Insert batch tracking record
        INSERT INTO [dbo].[BatchOperations] ([BatchId], [OperationType], [Status], [TotalEntries], [CreatedBy])
        SELECT @BatchId, 'INSERT', 1, COUNT(*), @CreatedBy
        FROM OPENJSON(@BatchData);
        
        -- Insert history entries from JSON
        INSERT INTO [dbo].[HistoryEntries_Hot] 
        ([LeadId], [FieldName], [OldValue], [NewValue], [ChangedAt], [ChangedBy], [Metadata], [Tier], [Checksum])
        SELECT 
            CAST(JSON_VALUE(value, '$.LeadId') AS INT),
            JSON_VALUE(value, '$.FieldName'),
            JSON_VALUE(value, '$.OldValue'),
            JSON_VALUE(value, '$.NewValue'),
            CAST(JSON_VALUE(value, '$.ChangedAt') AS DATETIME2),
            JSON_VALUE(value, '$.ChangedBy'),
            JSON_VALUE(value, '$.Metadata'),
            ISNULL(CAST(JSON_VALUE(value, '$.Tier') AS INT), 1),
            JSON_VALUE(value, '$.Checksum')
        FROM OPENJSON(@BatchData);
        
        SET @ProcessedCount = @@ROWCOUNT;
        
        -- Update batch status
        UPDATE [dbo].[BatchOperations] 
        SET [Status] = 2, -- Completed
            [ProcessedEntries] = @ProcessedCount,
            [CompletedAt] = GETUTCDATE()
        WHERE [BatchId] = @BatchId;
        
        COMMIT TRANSACTION;
        
        SELECT @ProcessedCount AS ProcessedCount, 0 AS ErrorCode, NULL AS ErrorMessage;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        SET @ErrorMessage = ERROR_MESSAGE();
        
        -- Update batch status to failed
        UPDATE [dbo].[BatchOperations] 
        SET [Status] = 3, -- Failed
            [ErrorMessage] = @ErrorMessage,
            [CompletedAt] = GETUTCDATE()
        WHERE [BatchId] = @BatchId;
        
        SELECT 0 AS ProcessedCount, ERROR_NUMBER() AS ErrorCode, @ErrorMessage AS ErrorMessage;
    END CATCH
END
GO

-- Create stored procedure for archival operations
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_ArchiveToWarmTier]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_ArchiveToWarmTier]
GO

CREATE PROCEDURE [dbo].[sp_ArchiveToWarmTier]
    @CutoffDate DATETIME2,
    @BatchSize INT = 1000
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ArchivedCount INT = 0;
    DECLARE @TotalArchived INT = 0;
    
    WHILE 1 = 1
    BEGIN
        BEGIN TRANSACTION;
        
        -- Get a batch of entries to archive
        WITH EntriesToArchive AS (
            SELECT TOP (@BatchSize) [Id]
            FROM [dbo].[HistoryEntries_Hot]
            WHERE [ChangedAt] < @CutoffDate
            ORDER BY [ChangedAt]
        )
        -- TODO: In production, this would copy to warm tier database first
        -- For now, we'll just mark them as archived
        UPDATE h
        SET [Tier] = 2, -- Warm tier
            [UpdatedAt] = GETUTCDATE()
        FROM [dbo].[HistoryEntries_Hot] h
        INNER JOIN EntriesToArchive e ON h.[Id] = e.[Id];
        
        SET @ArchivedCount = @@ROWCOUNT;
        SET @TotalArchived = @TotalArchived + @ArchivedCount;
        
        COMMIT TRANSACTION;
        
        -- Exit if no more records to process
        IF @ArchivedCount = 0
            BREAK;
    END
    
    SELECT @TotalArchived AS TotalArchived;
END
GO

-- Create function for generating cache keys
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[fn_GenerateCacheKey]') AND type in (N'FN', N'IF', N'TF'))
    DROP FUNCTION [dbo].[fn_GenerateCacheKey]
GO

CREATE FUNCTION [dbo].[fn_GenerateCacheKey]
(
    @LeadId INT = NULL,
    @FieldName NVARCHAR(100) = NULL,
    @StartDate DATETIME2 = NULL,
    @EndDate DATETIME2 = NULL,
    @Page INT = 1,
    @PageSize INT = 100
)
RETURNS NVARCHAR(500)
AS
BEGIN
    DECLARE @CacheKey NVARCHAR(500) = 'query:';
    
    IF @LeadId IS NOT NULL
        SET @CacheKey = @CacheKey + 'lead:' + CAST(@LeadId AS NVARCHAR(20)) + ':';
    
    IF @FieldName IS NOT NULL
        SET @CacheKey = @CacheKey + 'field:' + @FieldName + ':';
    
    IF @StartDate IS NOT NULL
        SET @CacheKey = @CacheKey + 'from:' + FORMAT(@StartDate, 'yyyyMMdd') + ':';
    
    IF @EndDate IS NOT NULL
        SET @CacheKey = @CacheKey + 'to:' + FORMAT(@EndDate, 'yyyyMMdd') + ':';
    
    SET @CacheKey = @CacheKey + 'page:' + CAST(@Page AS NVARCHAR(10)) + ':size:' + CAST(@PageSize AS NVARCHAR(10));
    
    RETURN @CacheKey;
END
GO

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON [dbo].[HistoryEntries_Hot] TO [CrmHistoryUser];
GRANT SELECT, INSERT, UPDATE ON [dbo].[BatchOperations] TO [CrmHistoryUser];
GRANT SELECT, INSERT ON [dbo].[PerformanceMetrics] TO [CrmHistoryUser];
GRANT EXECUTE ON [dbo].[sp_InsertHistoryBatch] TO [CrmHistoryUser];
GRANT EXECUTE ON [dbo].[sp_ArchiveToWarmTier] TO [CrmHistoryUser];

PRINT 'Hot tier database schema created successfully';
GO
