-- =============================================
-- CRM History System - Warm Tier Database Schema
-- Creates compressed tables for medium-term data (3-12 months)
-- Performance target: <200ms query response time
-- =============================================

USE [CrmHistory_Warm]
GO

-- Create the main history entries table for warm tier with compression
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[HistoryEntries_Warm]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[HistoryEntries_Warm](
        [Id] [bigint] IDENTITY(1,1) NOT NULL,
        [LeadId] [int] NOT NULL,
        [FieldName] [nvarchar](100) NOT NULL,
        [OldValue] [nvarchar](4000) NULL,
        [NewValue] [nvarchar](4000) NULL,
        [ChangedAt] [datetime2](7) NOT NULL,
        [ChangedBy] [nvarchar](100) NOT NULL,
        [Metadata] [nvarchar](8000) NULL,
        [Tier] [int] NOT NULL DEFAULT(2), -- 2 = Warm
        [Checksum] [nvarchar](100) NULL,
        [ArchivedAt] [datetime2](7) NOT NULL DEFAULT(GETUTCDATE()),
        [OriginalId] [bigint] NULL, -- Reference to original hot tier ID
        CONSTRAINT [PK_HistoryEntries_Warm] PRIMARY KEY CLUSTERED ([Id] ASC)
    ) ON [PRIMARY]
    WITH (DATA_COMPRESSION = PAGE) -- Enable page compression for storage optimization
END
GO

-- Create partitioned table for better performance with large datasets
-- Partition by month for optimal archival and query performance
IF NOT EXISTS (SELECT * FROM sys.partition_functions WHERE name = 'pf_HistoryByMonth')
BEGIN
    -- Create partition function for monthly partitions
    CREATE PARTITION FUNCTION pf_HistoryByMonth (datetime2)
    AS RANGE RIGHT FOR VALUES (
        '2023-01-01', '2023-02-01', '2023-03-01', '2023-04-01', '2023-05-01', '2023-06-01',
        '2023-07-01', '2023-08-01', '2023-09-01', '2023-10-01', '2023-11-01', '2023-12-01',
        '2024-01-01', '2024-02-01', '2024-03-01', '2024-04-01', '2024-05-01', '2024-06-01',
        '2024-07-01', '2024-08-01', '2024-09-01', '2024-10-01', '2024-11-01', '2024-12-01',
        '2025-01-01', '2025-02-01', '2025-03-01', '2025-04-01', '2025-05-01', '2025-06-01',
        '2025-07-01', '2025-08-01', '2025-09-01', '2025-10-01', '2025-11-01', '2025-12-01'
    );
END
GO

-- Create partition scheme
IF NOT EXISTS (SELECT * FROM sys.partition_schemes WHERE name = 'ps_HistoryByMonth')
BEGIN
    CREATE PARTITION SCHEME ps_HistoryByMonth
    AS PARTITION pf_HistoryByMonth
    ALL TO ([PRIMARY]);
END
GO

-- Create optimized indexes for warm tier queries
-- Primary index for lead-based queries with compression
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[HistoryEntries_Warm]') AND name = N'IX_HistoryEntries_Warm_LeadId_ChangedAt')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Warm_LeadId_ChangedAt] 
    ON [dbo].[HistoryEntries_Warm] ([LeadId] ASC, [ChangedAt] DESC)
    INCLUDE ([FieldName], [OldValue], [NewValue], [ChangedBy])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON,
          DATA_COMPRESSION = PAGE)
END
GO

-- Index for field-based queries with compression
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[HistoryEntries_Warm]') AND name = N'IX_HistoryEntries_Warm_FieldName_ChangedAt')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Warm_FieldName_ChangedAt] 
    ON [dbo].[HistoryEntries_Warm] ([FieldName] ASC, [ChangedAt] DESC)
    INCLUDE ([LeadId], [OldValue], [NewValue], [ChangedBy])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON,
          DATA_COMPRESSION = PAGE)
END
GO

-- Index for date-based queries optimized for archival
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[HistoryEntries_Warm]') AND name = N'IX_HistoryEntries_Warm_ChangedAt_Partitioned')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_HistoryEntries_Warm_ChangedAt_Partitioned] 
    ON [dbo].[HistoryEntries_Warm] ([ChangedAt] DESC)
    INCLUDE ([LeadId], [FieldName], [ChangedBy])
    WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, 
          DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON,
          DATA_COMPRESSION = PAGE)
END
GO

-- Create archival tracking table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ArchivalOperations]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ArchivalOperations](
        [Id] [bigint] IDENTITY(1,1) NOT NULL,
        [OperationType] [nvarchar](50) NOT NULL, -- 'HOT_TO_WARM', 'WARM_TO_COLD'
        [SourceTier] [int] NOT NULL,
        [TargetTier] [int] NOT NULL,
        [StartDate] [datetime2](7) NOT NULL,
        [EndDate] [datetime2](7) NOT NULL,
        [TotalRecords] [bigint] NOT NULL,
        [ProcessedRecords] [bigint] NOT NULL DEFAULT(0),
        [FailedRecords] [bigint] NOT NULL DEFAULT(0),
        [Status] [int] NOT NULL DEFAULT(0), -- 0=Pending, 1=InProgress, 2=Completed, 3=Failed
        [CreatedAt] [datetime2](7) NOT NULL DEFAULT(GETUTCDATE()),
        [StartedAt] [datetime2](7) NULL,
        [CompletedAt] [datetime2](7) NULL,
        [ErrorMessage] [nvarchar](max) NULL,
        [CompressionRatio] [decimal](5,2) NULL,
        [OriginalSizeMB] [decimal](10,2) NULL,
        [CompressedSizeMB] [decimal](10,2) NULL,
        CONSTRAINT [PK_ArchivalOperations] PRIMARY KEY CLUSTERED ([Id] ASC)
    ) ON [PRIMARY]
END
GO

-- Create stored procedure for receiving data from hot tier
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_ReceiveFromHotTier]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_ReceiveFromHotTier]
GO

CREATE PROCEDURE [dbo].[sp_ReceiveFromHotTier]
    @BatchData NVARCHAR(MAX), -- JSON array of history entries from hot tier
    @ArchivalId BIGINT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ProcessedCount INT = 0;
    DECLARE @ErrorMessage NVARCHAR(MAX) = NULL;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Insert history entries from hot tier
        INSERT INTO [dbo].[HistoryEntries_Warm] 
        ([LeadId], [FieldName], [OldValue], [NewValue], [ChangedAt], [ChangedBy], [Metadata], [Tier], [Checksum], [OriginalId])
        SELECT 
            CAST(JSON_VALUE(value, '$.LeadId') AS INT),
            JSON_VALUE(value, '$.FieldName'),
            JSON_VALUE(value, '$.OldValue'),
            JSON_VALUE(value, '$.NewValue'),
            CAST(JSON_VALUE(value, '$.ChangedAt') AS DATETIME2),
            JSON_VALUE(value, '$.ChangedBy'),
            JSON_VALUE(value, '$.Metadata'),
            2, -- Warm tier
            JSON_VALUE(value, '$.Checksum'),
            CAST(JSON_VALUE(value, '$.Id') AS BIGINT)
        FROM OPENJSON(@BatchData);
        
        SET @ProcessedCount = @@ROWCOUNT;
        
        -- Update archival operation status
        UPDATE [dbo].[ArchivalOperations] 
        SET [ProcessedRecords] = [ProcessedRecords] + @ProcessedCount
        WHERE [Id] = @ArchivalId;
        
        COMMIT TRANSACTION;
        
        SELECT @ProcessedCount AS ProcessedCount, 0 AS ErrorCode, NULL AS ErrorMessage;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        SET @ErrorMessage = ERROR_MESSAGE();
        
        -- Update archival operation with error
        UPDATE [dbo].[ArchivalOperations] 
        SET [Status] = 3, -- Failed
            [ErrorMessage] = @ErrorMessage,
            [CompletedAt] = GETUTCDATE()
        WHERE [Id] = @ArchivalId;
        
        SELECT 0 AS ProcessedCount, ERROR_NUMBER() AS ErrorCode, @ErrorMessage AS ErrorMessage;
    END CATCH
END
GO

-- Create stored procedure for archiving to cold tier
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_ArchiveToColdTier]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[sp_ArchiveToColdTier]
GO

CREATE PROCEDURE [dbo].[sp_ArchiveToColdTier]
    @CutoffDate DATETIME2,
    @BatchSize INT = 5000
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ArchivedCount INT = 0;
    DECLARE @TotalArchived INT = 0;
    DECLARE @ArchivalId BIGINT;
    
    -- Create archival operation record
    INSERT INTO [dbo].[ArchivalOperations] 
    ([OperationType], [SourceTier], [TargetTier], [StartDate], [EndDate], [TotalRecords], [Status])
    SELECT 'WARM_TO_COLD', 2, 3, @CutoffDate, GETUTCDATE(), COUNT(*), 1
    FROM [dbo].[HistoryEntries_Warm]
    WHERE [ChangedAt] < @CutoffDate;
    
    SET @ArchivalId = SCOPE_IDENTITY();
    
    WHILE 1 = 1
    BEGIN
        BEGIN TRANSACTION;
        
        -- Get a batch of entries to archive
        WITH EntriesToArchive AS (
            SELECT TOP (@BatchSize) [Id], [LeadId], [FieldName], [OldValue], [NewValue], 
                   [ChangedAt], [ChangedBy], [Metadata], [Checksum], [OriginalId]
            FROM [dbo].[HistoryEntries_Warm]
            WHERE [ChangedAt] < @CutoffDate
            ORDER BY [ChangedAt]
        )
        -- TODO: In production, this would export to Azure Blob Storage
        -- For now, we'll mark them as cold tier
        UPDATE w
        SET [Tier] = 3 -- Cold tier
        FROM [dbo].[HistoryEntries_Warm] w
        INNER JOIN EntriesToArchive e ON w.[Id] = e.[Id];
        
        SET @ArchivedCount = @@ROWCOUNT;
        SET @TotalArchived = @TotalArchived + @ArchivedCount;
        
        -- Update progress
        UPDATE [dbo].[ArchivalOperations] 
        SET [ProcessedRecords] = @TotalArchived
        WHERE [Id] = @ArchivalId;
        
        COMMIT TRANSACTION;
        
        -- Exit if no more records to process
        IF @ArchivedCount = 0
            BREAK;
    END
    
    -- Mark archival as completed
    UPDATE [dbo].[ArchivalOperations] 
    SET [Status] = 2, -- Completed
        [CompletedAt] = GETUTCDATE()
    WHERE [Id] = @ArchivalId;
    
    SELECT @TotalArchived AS TotalArchived, @ArchivalId AS ArchivalId;
END
GO

-- Create view for performance monitoring
IF EXISTS (SELECT * FROM sys.views WHERE object_id = OBJECT_ID(N'[dbo].[vw_WarmTierStatistics]'))
    DROP VIEW [dbo].[vw_WarmTierStatistics]
GO

CREATE VIEW [dbo].[vw_WarmTierStatistics]
AS
SELECT 
    COUNT(*) AS TotalEntries,
    COUNT(DISTINCT LeadId) AS UniqueLeads,
    COUNT(DISTINCT FieldName) AS UniqueFields,
    COUNT(DISTINCT ChangedBy) AS UniqueUsers,
    MIN(ChangedAt) AS OldestEntry,
    MAX(ChangedAt) AS NewestEntry,
    AVG(DATALENGTH(OldValue) + DATALENGTH(NewValue)) AS AvgEntrySizeBytes,
    SUM(DATALENGTH(OldValue) + DATALENGTH(NewValue)) AS TotalSizeBytes
FROM [dbo].[HistoryEntries_Warm]
WHERE Tier = 2;
GO

-- Create function for compression ratio calculation
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[fn_CalculateCompressionRatio]') AND type in (N'FN', N'IF', N'TF'))
    DROP FUNCTION [dbo].[fn_CalculateCompressionRatio]
GO

CREATE FUNCTION [dbo].[fn_CalculateCompressionRatio]()
RETURNS DECIMAL(5,2)
AS
BEGIN
    DECLARE @UncompressedSize BIGINT;
    DECLARE @CompressedSize BIGINT;
    DECLARE @CompressionRatio DECIMAL(5,2);
    
    -- Get uncompressed size estimate
    SELECT @UncompressedSize = SUM(
        8 + -- Id
        4 + -- LeadId
        DATALENGTH(FieldName) + 
        ISNULL(DATALENGTH(OldValue), 0) + 
        ISNULL(DATALENGTH(NewValue), 0) + 
        8 + -- ChangedAt
        DATALENGTH(ChangedBy) + 
        ISNULL(DATALENGTH(Metadata), 0) + 
        4 + -- Tier
        ISNULL(DATALENGTH(Checksum), 0) + 
        8 + -- ArchivedAt
        8   -- OriginalId
    )
    FROM [dbo].[HistoryEntries_Warm];
    
    -- Get actual compressed size from system tables
    SELECT @CompressedSize = SUM(a.used_pages * 8192)
    FROM sys.allocation_units a
    INNER JOIN sys.partitions p ON a.container_id = p.partition_id
    INNER JOIN sys.objects o ON p.object_id = o.object_id
    WHERE o.name = 'HistoryEntries_Warm';
    
    IF @UncompressedSize > 0
        SET @CompressionRatio = CAST(@CompressedSize AS DECIMAL(18,2)) / CAST(@UncompressedSize AS DECIMAL(18,2));
    ELSE
        SET @CompressionRatio = 1.0;
    
    RETURN @CompressionRatio;
END
GO

-- Grant permissions
GRANT SELECT, INSERT, UPDATE ON [dbo].[HistoryEntries_Warm] TO [CrmHistoryUser];
GRANT SELECT, INSERT, UPDATE ON [dbo].[ArchivalOperations] TO [CrmHistoryUser];
GRANT SELECT ON [dbo].[vw_WarmTierStatistics] TO [CrmHistoryUser];
GRANT EXECUTE ON [dbo].[sp_ReceiveFromHotTier] TO [CrmHistoryUser];
GRANT EXECUTE ON [dbo].[sp_ArchiveToColdTier] TO [CrmHistoryUser];

PRINT 'Warm tier database schema created successfully';
GO
