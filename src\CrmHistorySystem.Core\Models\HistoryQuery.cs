using System.ComponentModel.DataAnnotations;

namespace CrmHistorySystem.Core.Models;

/// <summary>
/// Represents a query for retrieving history entries with filtering and pagination support.
/// Optimized for efficient querying across tiered storage systems.
/// </summary>
public class HistoryQuery
{
    /// <summary>
    /// Filter by specific lead ID
    /// </summary>
    public int? LeadId { get; set; }

    /// <summary>
    /// Filter by specific field name
    /// </summary>
    [StringLength(100)]
    public string? FieldName { get; set; }

    /// <summary>
    /// Filter by multiple field names
    /// </summary>
    public IEnumerable<string>? FieldNames { get; set; }

    /// <summary>
    /// Filter by user who made the change
    /// </summary>
    [StringLength(100)]
    public string? ChangedBy { get; set; }

    /// <summary>
    /// Start date for the query range (inclusive)
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// End date for the query range (inclusive)
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Page number for pagination (1-based)
    /// </summary>
    [Range(1, int.MaxValue)]
    public int Page { get; set; } = 1;

    /// <summary>
    /// Number of records per page
    /// </summary>
    [Range(1, 10000)]
    public int PageSize { get; set; } = 100;

    /// <summary>
    /// Sort order for results
    /// </summary>
    public SortOrder SortOrder { get; set; } = SortOrder.Descending;

    /// <summary>
    /// Field to sort by
    /// </summary>
    public SortField SortBy { get; set; } = SortField.ChangedAt;

    /// <summary>
    /// Include metadata in results
    /// </summary>
    public bool IncludeMetadata { get; set; } = false;

    /// <summary>
    /// Specific storage tiers to query
    /// </summary>
    public IEnumerable<StorageTier>? Tiers { get; set; }

    /// <summary>
    /// Maximum number of records to return (overrides pagination)
    /// </summary>
    [Range(1, 100000)]
    public int? MaxResults { get; set; }

    /// <summary>
    /// Determines which storage tiers should be queried based on the date range
    /// </summary>
    /// <param name="hotTierDays">Number of days for hot tier retention</param>
    /// <param name="warmTierDays">Number of days for warm tier retention</param>
    /// <returns>List of storage tiers to query</returns>
    public IEnumerable<StorageTier> GetRequiredTiers(int hotTierDays = 90, int warmTierDays = 365)
    {
        var tiers = new List<StorageTier>();
        var now = DateTime.UtcNow;

        // If no date range specified, query all tiers
        if (!StartDate.HasValue && !EndDate.HasValue)
        {
            return new[] { StorageTier.Hot, StorageTier.Warm, StorageTier.Cold };
        }

        var queryStart = StartDate ?? DateTime.MinValue;
        var queryEnd = EndDate ?? now;

        // Check if query overlaps with hot tier
        var hotTierStart = now.AddDays(-hotTierDays);
        if (queryEnd >= hotTierStart)
        {
            tiers.Add(StorageTier.Hot);
        }

        // Check if query overlaps with warm tier
        var warmTierStart = now.AddDays(-warmTierDays);
        var warmTierEnd = hotTierStart;
        if (queryEnd >= warmTierStart && queryStart <= warmTierEnd)
        {
            tiers.Add(StorageTier.Warm);
        }

        // Check if query overlaps with cold tier
        var coldTierEnd = warmTierStart;
        if (queryStart <= coldTierEnd)
        {
            tiers.Add(StorageTier.Cold);
        }

        return tiers;
    }

    /// <summary>
    /// Calculates the skip count for pagination
    /// </summary>
    /// <returns>Number of records to skip</returns>
    public int GetSkipCount() => (Page - 1) * PageSize;

    /// <summary>
    /// Validates the query parameters
    /// </summary>
    /// <returns>True if the query is valid</returns>
    public bool IsValid()
    {
        if (StartDate.HasValue && EndDate.HasValue && StartDate > EndDate)
            return false;

        if (Page < 1 || PageSize < 1)
            return false;

        if (MaxResults.HasValue && MaxResults < 1)
            return false;

        return true;
    }

    /// <summary>
    /// Creates a copy of the query for a specific storage tier
    /// </summary>
    /// <param name="tier">Target storage tier</param>
    /// <param name="hotTierDays">Hot tier retention days</param>
    /// <param name="warmTierDays">Warm tier retention days</param>
    /// <returns>Query adjusted for the specified tier</returns>
    public HistoryQuery ForTier(StorageTier tier, int hotTierDays = 90, int warmTierDays = 365)
    {
        var query = new HistoryQuery
        {
            LeadId = LeadId,
            FieldName = FieldName,
            FieldNames = FieldNames,
            ChangedBy = ChangedBy,
            StartDate = StartDate,
            EndDate = EndDate,
            Page = Page,
            PageSize = PageSize,
            SortOrder = SortOrder,
            SortBy = SortBy,
            IncludeMetadata = IncludeMetadata,
            MaxResults = MaxResults
        };

        // Adjust date range based on tier boundaries
        var now = DateTime.UtcNow;
        
        switch (tier)
        {
            case StorageTier.Hot:
                var hotStart = now.AddDays(-hotTierDays);
                if (!query.StartDate.HasValue || query.StartDate < hotStart)
                    query.StartDate = hotStart;
                break;
                
            case StorageTier.Warm:
                var warmStart = now.AddDays(-warmTierDays);
                var warmEnd = now.AddDays(-hotTierDays);
                if (!query.StartDate.HasValue || query.StartDate < warmStart)
                    query.StartDate = warmStart;
                if (!query.EndDate.HasValue || query.EndDate > warmEnd)
                    query.EndDate = warmEnd;
                break;
                
            case StorageTier.Cold:
                var coldEnd = now.AddDays(-warmTierDays);
                if (!query.EndDate.HasValue || query.EndDate > coldEnd)
                    query.EndDate = coldEnd;
                break;
        }

        return query;
    }
}

/// <summary>
/// Sort order options for query results
/// </summary>
public enum SortOrder
{
    Ascending,
    Descending
}

/// <summary>
/// Available fields for sorting query results
/// </summary>
public enum SortField
{
    ChangedAt,
    LeadId,
    FieldName,
    ChangedBy
}
